package fm.lizhi.ocean.wave.generic.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.List;

public class GsonUtil {

    private static final Gson gson = new GsonBuilder()
            .registerTypeAdapter(Date.class, new DateSerializer())
            .registerTypeAdapter(Date.class, new DateDeserializer())
            .create();

    /**
     * 将JSON字符串按默认的规则反序列化为对象
     *
     * @param content JSON字符串
     * @param type    要反序列化的类型
     * @param <T>     反序列化类型泛型
     * @return 反序列化的对象
     */
    public static <T> T fromJsonString(String content, Class<T> type) {
        return gson.fromJson(content, type);
    }

    public static Object fromJsonString(String content, Type type) {
        return gson.fromJson(content, type);
    }

    public static <T> T fromJsonString(String content, Class<T> type, Class<?>... genericType){
        Type mergeType = TypeToken.getParameterized(type, genericType).getType();
        return gson.fromJson(content, mergeType);
    }

    public static <T> List<T> loadsArray(String jsonArrayStr, Class<T> genericType) {
        return fromJsonString(jsonArrayStr, List.class, genericType);
    }

    public static String toJson(Object object) {
        return gson.toJson(object);
    }
}
