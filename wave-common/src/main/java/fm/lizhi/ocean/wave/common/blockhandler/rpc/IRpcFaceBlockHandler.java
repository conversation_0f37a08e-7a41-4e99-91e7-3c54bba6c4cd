package fm.lizhi.ocean.wave.common.blockhandler.rpc;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.blockhandler.fallback.rpc.RpcFallbackContext;

/**
 * <AUTHOR>
 * @date 2023/12/20 12:35
 */
public interface IRpcFaceBlockHandler<E extends BlockException> extends IRpcBlockHandler<Class<?>, E> {

    /**
     * 降级的目标接口
     * 比如：给LiveNewService接口降级，则返回LiveNewService.class
     * @return
     */
    @Override
    Class<?> getTarget();

    @Override
    default Result<?> handle(RpcFallbackContext context, E be) throws Exception{
        //自定义类降级不会调用该降级方法
        throw be;
    }

}
