package fm.lizhi.ocean.wave.common.model.result;

import lombok.*;

import java.util.List;

/**
 * 送审结果列表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckSingleDataResult {


    /**
     * 响应Code，定义枚举, fm.lizhi.content.review.constant.CheckDataResultCodeEnum
     */
    private int resultCode;

    /**
     * 鉴定结果
     */
    private List<DiscernResult> discernResults;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DiscernResult {

        /**
         * 透传字段业务id, 对应 contentId
         */
        private String contentId;

        /**
         * 命中风险类型, 定义枚举fm.lizhi.content.review.constant.CheckDataRiskTypeEnum
         */
        private int riskTypes;

        /**
         * 命中详情 ["法轮功","娇喘"]
         */
        private String hitDetails;
    }

}
