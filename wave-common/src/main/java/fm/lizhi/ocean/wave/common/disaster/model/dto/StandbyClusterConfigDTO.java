package fm.lizhi.ocean.wave.common.disaster.model.dto;

import lombok.Data;

@Data
public class StandbyClusterConfigDTO {

    private Integer appId;

    /**
     * 灾备集群域名
     */
    private String disasterStandbyDomain;

    /**
     * 是否开启灾备
     */
    private Boolean openDisasterRecovery;

    /**
     * 再一次操作消费者版本，和原来的不一致时，会再次操作kafka消费者启停
     */
    private long againExecuteConsumerVersion;

    /**
     * 再一次推送罗马时间戳，和原来的不一致时，会再次推送罗马消息
     */
    private long againPushRomaMsgVersion;


}
