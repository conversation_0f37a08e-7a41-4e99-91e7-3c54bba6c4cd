package fm.lizhi.ocean.wave.permission.core.manager.statement.test.factory;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.ConditionGroup;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.LogicSymbol;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2025/2/28 19:48
 */
@Slf4j
public class ConditionGroupTest {

    private AbstractCondition trueCondition = new AbstractCondition<Object>(new ConditionDTO()
            .setValue(1)
            .setOperateSymbol(OperateSymbol.NUMBER_EQ.getValue())
            .setAttribute("user.userId")) {
        @Override
        public boolean innerExecute(ExecutorContext context, IStatement statement) {
            return true;
        }
    };

    private AbstractCondition falseCondition = new AbstractCondition<Object>(new ConditionDTO()
            .setValue(1)
            .setOperateSymbol(OperateSymbol.NUMBER_EQ.getValue())
            .setAttribute("user.userId")) {
        @Override
        public boolean innerExecute(ExecutorContext context, IStatement statement) {
            return false;
        }
    };

    /**
     * 不能被执行到的条件
     */
    private AbstractCondition cannotExeCondition = new AbstractCondition<Object>(new ConditionDTO()
            .setValue(1)
            .setOperateSymbol(OperateSymbol.NUMBER_EQ.getValue())
            .setAttribute("user.userId")) {
        @Override
        public boolean innerExecute(ExecutorContext context, IStatement statement) {
            int a = 1/0;
            return true;
        }
    };

    @Test
    public void testOr(){

        ConditionGroup group1 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.OR)
                .conditionList(Lists.newArrayList(
                        trueCondition
                        , falseCondition
                ))
                .build();
        boolean trueRes = group1.innerExecute(null, null);
        log.info("trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        ConditionGroup group2 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.OR)
                .conditionList(Lists.newArrayList(
                        falseCondition
                ))
                .build();
        boolean falseRes = group2.innerExecute(null, null);
        log.info("falseRes:{}", falseRes);
        Assert.assertFalse(falseRes);

        ConditionGroup group3 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.OR)
                .conditionList(Lists.newArrayList(
                        falseCondition
                        , trueCondition
                        , falseCondition
                ))
                .build();
        boolean group3Res = group3.innerExecute(null, null);
        log.info("group3Res:{}", group3Res);
        Assert.assertTrue(group3Res);

        ConditionGroup group4 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.OR)
                .conditionList(Lists.newArrayList(
                        trueCondition
                        , cannotExeCondition
                ))
                .build();
        boolean group4Res = group4.innerExecute(null, null);
        log.info("group4Res:{}", group4Res);
        Assert.assertTrue(group4Res);

        ConditionGroup group5 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.OR)
                .conditionList(Lists.newArrayList(
                        falseCondition
                        , falseCondition
                ))
                .conditionGroup(group2)
                .build();
        boolean group5Res = group5.innerExecute(null, null);
        log.info("group5Res:{}", group5Res);
        Assert.assertFalse(group5Res);

        ConditionGroup group6 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.OR)
                .conditionList(Lists.newArrayList(
                        falseCondition
                        , falseCondition
                ))
                .conditionGroup(group3)
                .build();
        boolean group6Res = group6.innerExecute(null, null);
        log.info("group6Res:{}", group6Res);
        Assert.assertTrue(group6Res);

    }

    @Test
    public void testAnd(){

        ConditionGroup group1 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.AND)
                .conditionList(Lists.newArrayList(
                        trueCondition
                        , falseCondition
                ))
                .build();
        boolean falseRes = group1.innerExecute(null, null);
        log.info("falseRes:{}", falseRes);
        Assert.assertFalse(falseRes);

        ConditionGroup group2 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.AND)
                .conditionList(Lists.newArrayList(
                        trueCondition
                ))
                .build();
        boolean trueRes = group2.innerExecute(null, null);
        log.info("trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        ConditionGroup group3 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.AND)
                .conditionList(Lists.newArrayList(
                        falseCondition
                        , trueCondition
                        , falseCondition
                ))
                .build();
        boolean group3Res = group3.innerExecute(null, null);
        log.info("group3Res:{}", group3Res);
        Assert.assertFalse(group3Res);

        ConditionGroup group4 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.AND)
                .conditionList(Lists.newArrayList(
                        falseCondition
                        , cannotExeCondition
                ))
                .build();
        boolean group4Res = group4.innerExecute(null, null);
        log.info("group4Res:{}", group4Res);
        Assert.assertFalse(group4Res);

        ConditionGroup group5 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.AND)
                .conditionList(Lists.newArrayList(
                        trueCondition
                        , trueCondition
                ))
                .conditionGroup(group2)
                .build();
        boolean group5Res = group5.innerExecute(null, null);
        log.info("group5Res:{}", group5Res);
        Assert.assertTrue(group5Res);

        ConditionGroup group6 = ConditionGroup.builder()
                .logicSymbol(LogicSymbol.AND)
                .conditionList(Lists.newArrayList(
                        trueCondition
                        , trueCondition
                ))
                .conditionGroup(group3)
                .build();
        boolean group6Res = group6.innerExecute(null, null);
        log.info("group6Res:{}", group6Res);
        Assert.assertFalse(group6Res);

    }

}
