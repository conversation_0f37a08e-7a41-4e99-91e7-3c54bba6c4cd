package fm.lizhi.ocean.wave.permission.core.manager.statement.test.factory;

import com.sun.tools.javac.util.List;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.constants.LiveModeEnum;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.EngineLive;
import fm.lizhi.ocean.wave.permission.core.manager.statement.EngineRequest;
import fm.lizhi.ocean.wave.permission.core.manager.statement.EngineUser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.*;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.BeanExecutorCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.BeanExecutorValue;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.number.*;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import fm.lizhi.ocean.wave.user.api.UserGroupService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import java.util.function.Function;

/**
 * 条件发生变动时，需要保证该单元测试无异常
 * <AUTHOR>
 * @date 2025/2/28 17:00
 */
@Slf4j
public class ConditionTest {

    String clientVersionKey = "request.clientVersion";
    String liveModelKey = "live.liveModel";
    String userIdKey = "user.userId";
    String executorKey = "executor";

    @Test
    public void testBeanExecutorCondition() {
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(executorKey);
        conditionDTO.setOperateSymbol(OperateSymbol.BEAN_EXECUTOR.getValue());

        Function<String, AbstractBeanCondition> getBeanFunction = (beanName) -> {
            if (beanName.equals("null")) {
                return null;
            }

            return new AbstractBeanCondition() {
                @Override
                protected boolean doExecute(ExecutorContext context, IStatement statement) {
                    if (beanName.equals("true")) {
                        return true;
                    }
                    if (beanName.equals("false")) {
                        return false;
                    }
                    if (beanName.equals("exception")) {
                        throw new RuntimeException("exception");
                    }
                    return true;
                }
            };
        };

        // bean不存在 false
        conditionDTO.setValue(BeanExecutorValue.builder()
                .name("null")
                .cacheSecond(0)
                .build());
        BeanExecutorCondition condition = new BeanExecutorCondition(conditionDTO, getBeanFunction);
        boolean notExistRes = condition.innerExecute(ExecutorContext.builder().build(), null);
        log.info("testBeanExecutorCondition notExistRes:{}", notExistRes);
        Assert.assertFalse(notExistRes);

        // bean返回true true
        conditionDTO.setValue(BeanExecutorValue.builder()
                .name("true")
                .cacheSecond(0)
                .build());
        BeanExecutorCondition condition2 = new BeanExecutorCondition(conditionDTO, getBeanFunction);
        boolean trueRes = condition2.innerExecute(ExecutorContext.builder().build(), null);
        log.info("testBeanExecutorCondition trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        // bean返回false false
        conditionDTO.setValue(BeanExecutorValue.builder()
                .name("false")
                .cacheSecond(0)
                .build());
        BeanExecutorCondition condition3 = new BeanExecutorCondition(conditionDTO, getBeanFunction);
        boolean falseRes = condition3.innerExecute(ExecutorContext.builder().build(), null);
        log.info("testBeanExecutorCondition falseRes:{}", falseRes);
        Assert.assertFalse(falseRes);

        // bean执行异常 false
        conditionDTO.setValue(BeanExecutorValue.builder()
                .name("exception")
                .cacheSecond(0)
                .build());
        BeanExecutorCondition condition4 = new BeanExecutorCondition(conditionDTO, getBeanFunction);
        boolean exceptionRes = condition4.innerExecute(ExecutorContext.builder().build(), null);
        log.info("testBeanExecutorCondition exceptionRes:{}", exceptionRes);
        Assert.assertFalse(exceptionRes);
    }

    @Test
    public void testInUserGroupCondition() {
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(userIdKey);
        conditionDTO.setOperateSymbol(OperateSymbol.IN_USERGROUP.getValue());
        conditionDTO.setValue(1);

        long inUserId = 1;
        long notInUserId = 2;

        UserGroupService userGroupService = (groupId, userId, userIdType) -> {
            if (userId == inUserId) {
                return RpcResult.success(true);
            }
            if (userId == notInUserId) {
                return RpcResult.success(false);
            }
            return RpcResult.fail(GeneralRCode.GENERAL_RCODE_SERVER_BUSY);
        };

        InUserGroupCondition condition = new InUserGroupCondition(conditionDTO, userGroupService);

        // 测试用例1：完全匹配
        EngineUser request1 = EngineUser.builder().userId(inUserId).build();
        boolean trueRes = condition.innerExecute(ExecutorContext.builder().user(request1).build(), null);
        log.info("testInUserGroupCondition trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        // 不在组中
        EngineUser request2 = EngineUser.builder().userId(notInUserId).build();
        boolean falseRes = condition.innerExecute(ExecutorContext.builder().user(request2).build(), null);
        log.info("testInUserGroupCondition falseRes:{}", falseRes);
        Assert.assertFalse(falseRes);

        // 空值
        EngineUser request3 = EngineUser.builder().build();
        boolean nullRes = condition.innerExecute(ExecutorContext.builder().user(request3).build(), null);
        log.info("testInUserGroupCondition nullRes:{}", nullRes);
        Assert.assertFalse(falseRes);

        // 服务异常
        EngineUser request4 = EngineUser.builder().userId(4L).build();
        boolean errorRes = condition.innerExecute(ExecutorContext.builder().user(request4).build(), null);
        log.info("testInUserGroupCondition errorRes:{}", errorRes);
        Assert.assertFalse(errorRes);
    }

    @Test
    public void testNotInUserGroupCondition() {
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(userIdKey);
        conditionDTO.setOperateSymbol(OperateSymbol.IN_USERGROUP.getValue());
        conditionDTO.setValue(1);

        long inUserId = 1;
        long notInUserId = 2;

        UserGroupService userGroupService = (groupId, userId, userIdType) -> {
            if (userId == inUserId) {
                return RpcResult.success(true);
            }
            if (userId == notInUserId) {
                return RpcResult.success(false);
            }
            return RpcResult.fail(GeneralRCode.GENERAL_RCODE_SERVER_BUSY);
        };

        NotInUserGroupCondition condition = new NotInUserGroupCondition(conditionDTO, userGroupService);

        // 测试用例1：完全匹配
        EngineUser request1 = EngineUser.builder().userId(inUserId).build();
        boolean falseRes = condition.innerExecute(ExecutorContext.builder().user(request1).build(), null);
        log.info("testNotInUserGroupCondition falseRes:{}", falseRes);
        Assert.assertFalse(falseRes);

        // 不在组中
        EngineUser request2 = EngineUser.builder().userId(notInUserId).build();
        boolean trueRes = condition.innerExecute(ExecutorContext.builder().user(request2).build(), null);
        log.info("testNotInUserGroupCondition trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        // 空值
        EngineUser request3 = EngineUser.builder().build();
        boolean nullRes = condition.innerExecute(ExecutorContext.builder().user(request3).build(), null);
        log.info("testNotInUserGroupCondition nullRes:{}", nullRes);
        Assert.assertFalse(falseRes);

        // 服务异常
        EngineUser request4 = EngineUser.builder().userId(4L).build();
        boolean errorRes = condition.innerExecute(ExecutorContext.builder().user(request4).build(), null);
        log.info("testNotInUserGroupCondition errorRes:{}", errorRes);
        Assert.assertFalse(errorRes);
    }

    @Test
    public void testStringEqCondition() {
        // 准备条件：直播模式等于NORMAL_AMUSEMENT
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(liveModelKey);
        conditionDTO.setOperateSymbol(OperateSymbol.STRING_EQ.getValue());
        conditionDTO.setValue(LiveModeEnum.NORMAL_AMUSEMENT.name());
        StringEqCondition condition = new StringEqCondition(conditionDTO);

        // 测试用例1：完全匹配
        EngineLive request1 = EngineLive.builder().liveModel(LiveModeEnum.NORMAL_AMUSEMENT.name()).build();
        boolean trueRes = condition.innerExecute(ExecutorContext.builder().live(request1).build(), null);
        log.info("testStringEqCondition trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        // 测试用例2：大小写敏感
        EngineLive request2 = EngineLive.builder().liveModel("normal_amusement").build();
        boolean caseSensitiveRes = condition.innerExecute(ExecutorContext.builder().live(request2).build(), null);
        log.info("testStringEqCondition caseSensitiveRes:{}", caseSensitiveRes);
        Assert.assertFalse(caseSensitiveRes);

        // 测试用例3：空值处理
        EngineLive request3 = EngineLive.builder().liveModel("").build();
        boolean emptyRes = condition.innerExecute(ExecutorContext.builder().live(request3).build(), null);
        log.info("testStringEqCondition emptyRes:{}", emptyRes);
        Assert.assertFalse(emptyRes);

        // 测试用例4：null值处理
        EngineLive request4 = EngineLive.builder().build();
        boolean nullRes = condition.innerExecute(ExecutorContext.builder().live(request4).build(), null);
        log.info("testStringEqCondition nullRes:{}", nullRes);
        Assert.assertFalse(nullRes);
    }


    @Test
    public void testInCondition() {
        // 准备条件：属性值在["NORMAL_AMUSEMENT","NORMAL_VOCAL_ROOM"]中时通过
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(liveModelKey);
        conditionDTO.setOperateSymbol(OperateSymbol.IN.getValue());
        conditionDTO.setValue(List.of(LiveModeEnum.NORMAL_AMUSEMENT.name(), LiveModeEnum.NORMAL_VOCAL_ROOM.name()));
        InCondition condition = new InCondition(conditionDTO);

        // 测试用例1：值在集合中
        EngineLive request1 = EngineLive.builder().liveModel(LiveModeEnum.NORMAL_AMUSEMENT.name()).build();
        boolean trueRes = condition.innerExecute(ExecutorContext.builder().live(request1).build(), null);
        log.info("testInCondition trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        // 测试用例2：值不在集合中
        EngineLive request2 = EngineLive.builder().liveModel(LiveModeEnum.NORMAL_LIVE.name()).build();
        boolean falseRes = condition.innerExecute(ExecutorContext.builder().live(request2).build(), null);
        log.info("testInCondition falseRes:{}", falseRes);
        Assert.assertFalse(falseRes);

        // 测试用例3：属性值为null
        EngineLive request3 = EngineLive.builder().build();
        boolean nullRes = condition.innerExecute(ExecutorContext.builder().live(request3).build(), null);
        log.info("testInCondition nullRes:{}", nullRes);
        Assert.assertFalse(nullRes);

        // 测试用例4：属性值为空字符串
        EngineLive request4 = EngineLive.builder().liveModel("").build();
        boolean emptyRes = condition.innerExecute(ExecutorContext.builder().live(request4).build(), null);
        log.info("testInCondition emptyRes:{}", emptyRes);
        Assert.assertFalse(emptyRes);
    }

    @Test
    public void testNotInCondition() {
        // 准备条件：属性值不在["NORMAL_AMUSEMENT","NORMAL_VOCAL_ROOM"]中时通过
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(liveModelKey);
        conditionDTO.setOperateSymbol(OperateSymbol.NOT_IN.getValue());
        conditionDTO.setValue(List.of(LiveModeEnum.NORMAL_AMUSEMENT.name(), LiveModeEnum.NORMAL_VOCAL_ROOM.name()));
        NotInCondition condition = new NotInCondition(conditionDTO);

        // 测试用例1：值在集合中
        EngineLive request1 = EngineLive.builder().liveModel(LiveModeEnum.NORMAL_AMUSEMENT.name()).build();
        boolean falseRes = condition.innerExecute(ExecutorContext.builder().live(request1).build(), null);
        log.info("testNotInCondition falseRes:{}", falseRes);
        Assert.assertFalse(falseRes);

        // 测试用例2：值不在集合中
        EngineLive request2 = EngineLive.builder().liveModel(LiveModeEnum.NORMAL_LIVE.name()).build();
        boolean trueRes = condition.innerExecute(ExecutorContext.builder().live(request2).build(), null);
        log.info("testNotInCondition trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        // 测试用例3：属性值为null
        EngineLive request3 = EngineLive.builder().build();
        boolean nullRes = condition.innerExecute(ExecutorContext.builder().live(request3).build(), null);
        log.info("testNotInCondition nullRes:{}", nullRes);
        Assert.assertFalse(nullRes);

        // 测试用例4：属性值为空字符串
        EngineLive request4 = EngineLive.builder().liveModel("").build();
        boolean emptyRes = condition.innerExecute(ExecutorContext.builder().live(request4).build(), null);
        log.info("testNotInCondition emptyRes:{}", emptyRes);
        Assert.assertFalse(emptyRes);
    }

    @Test
    public void testNumberEqCondition() {
        // 准备条件：clientVersion等于5
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(clientVersionKey);
        conditionDTO.setOperateSymbol(OperateSymbol.NUMBER_EQ.getValue());
        conditionDTO.setValue(5);
        NumberEqCondition condition = new NumberEqCondition(conditionDTO);

        // 测试用例1：精确匹配
        EngineRequest request1 = EngineRequest.builder().clientVersion("5").build();
        boolean trueRes = condition.innerExecute(ExecutorContext.builder().request(request1).build(), null);
        log.info("testNumberEqCondition trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        // 测试用例2：数字字符串格式差异
        EngineRequest request2 = EngineRequest.builder().clientVersion("5.0").build();
        boolean formatDiffRes = condition.innerExecute(ExecutorContext.builder().request(request2).build(), null);
        log.info("testNumberEqCondition formatDiffRes:{}", formatDiffRes);
        Assert.assertTrue(formatDiffRes); // 取决于是否需要支持小数转换

        // 测试用例3：类型异常处理
        EngineRequest request3 = EngineRequest.builder().clientVersion("five").build();
        boolean invalidRes = condition.innerExecute(ExecutorContext.builder().request(request3).build(), null);
        log.info("testNumberEqCondition invalidRes:{}", invalidRes);
        Assert.assertFalse(invalidRes);

        // 测试用例5：空值
        EngineRequest request5 = EngineRequest.builder().clientVersion("").build();
        boolean emptyRes = condition.innerExecute(ExecutorContext.builder().request(request5).build(), null);
        log.info("testNumberGeCondition emptyRes:{}", emptyRes);
        Assert.assertFalse(emptyRes);

        // 测试用例6：null值
        EngineRequest request6 = EngineRequest.builder().build();
        boolean nullRes = condition.innerExecute(ExecutorContext.builder().request(request6).build(), null);
        log.info("testNumberGeCondition nullRes:{}", nullRes);
        Assert.assertFalse(nullRes);

    }

    @Test
    public void testNumberLtCondition() {
        // 准备条件：clientVersion < 1000
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(clientVersionKey);
        conditionDTO.setOperateSymbol(OperateSymbol.LT.getValue());
        conditionDTO.setValue(1000);
        NumberLtCondition condition = new NumberLtCondition(conditionDTO);

        // 测试用例1：刚好小于
        EngineRequest request1 = EngineRequest.builder().clientVersion("999").build();
        boolean trueRes = condition.innerExecute(ExecutorContext.builder().request(request1).build(), null);
        log.info("testNumberLtCondition trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        // 测试用例2：等于边界值
        EngineRequest request2 = EngineRequest.builder().clientVersion("1000").build();
        boolean equalRes = condition.innerExecute(ExecutorContext.builder().request(request2).build(), null);
        log.info("testNumberLtCondition equalRes:{}", equalRes);
        Assert.assertFalse(equalRes);

        // 测试用例3：非数值输入
        EngineRequest request3 = EngineRequest.builder().clientVersion("1k").build();
        boolean invalidRes = condition.innerExecute(ExecutorContext.builder().request(request3).build(), null);
        log.info("testNumberGeCondition invalidRes:{}", invalidRes);
        Assert.assertFalse(invalidRes);

        // 测试用例5：空值
        EngineRequest request5 = EngineRequest.builder().clientVersion("").build();
        boolean emptyRes = condition.innerExecute(ExecutorContext.builder().request(request5).build(), null);
        log.info("testNumberGeCondition emptyRes:{}", emptyRes);
        Assert.assertFalse(emptyRes);

        // 测试用例6：null值
        EngineRequest request6 = EngineRequest.builder().build();
        boolean nullRes = condition.innerExecute(ExecutorContext.builder().request(request6).build(), null);
        log.info("testNumberGeCondition nullRes:{}", nullRes);
        Assert.assertFalse(nullRes);
    }


    @Test
    public void testNumberGeCondition() {
        // 准备条件：clientVersion >= 18
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(clientVersionKey);
        conditionDTO.setOperateSymbol(OperateSymbol.GE.getValue());
        conditionDTO.setValue(18);
        NumberGeCondition condition = new NumberGeCondition(conditionDTO);

        // 测试用例1：等于边界值
        EngineRequest request1 = EngineRequest.builder().clientVersion("18").build();
        boolean equalRes = condition.innerExecute(ExecutorContext.builder().request(request1).build(), null);
        log.info("testNumberGeCondition equalRes:{}", equalRes);
        Assert.assertTrue(equalRes);

        // 测试用例2：大于边界值
        EngineRequest request2 = EngineRequest.builder().clientVersion("20").build();
        boolean gtRes = condition.innerExecute(ExecutorContext.builder().request(request2).build(), null);
        log.info("testNumberGeCondition gtRes:{}", gtRes);
        Assert.assertTrue(gtRes);

        // 测试用例3：小于边界值
        EngineRequest request3 = EngineRequest.builder().clientVersion("16").build();
        boolean ltRes = condition.innerExecute(ExecutorContext.builder().request(request3).build(), null);
        log.info("testNumberGeCondition ltRes:{}", ltRes);
        Assert.assertFalse(ltRes);

        // 测试用例4：无效数值
        EngineRequest request4 = EngineRequest.builder().clientVersion("abc").build();
        boolean invalidRes = condition.innerExecute(ExecutorContext.builder().request(request4).build(), null);
        log.info("testNumberGeCondition invalidRes:{}", invalidRes);
        Assert.assertFalse(invalidRes);

        // 测试用例5：空值
        EngineRequest request5 = EngineRequest.builder().clientVersion("").build();
        boolean emptyRes = condition.innerExecute(ExecutorContext.builder().request(request5).build(), null);
        log.info("testNumberGeCondition emptyRes:{}", emptyRes);
        Assert.assertFalse(emptyRes);

        // 测试用例6：null值
        EngineRequest request6 = EngineRequest.builder().build();
        boolean nullRes = condition.innerExecute(ExecutorContext.builder().request(request6).build(), null);
        log.info("testNumberGeCondition nullRes:{}", nullRes);
        Assert.assertFalse(nullRes);
    }

    @Test
    public void testNumberLe(){
        //要求版本号小于等于2
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(clientVersionKey);
        conditionDTO.setOperateSymbol(OperateSymbol.LE.getValue());
        conditionDTO.setValue(2);
        NumberLeCondition condition = new NumberLeCondition(conditionDTO);
        EngineRequest request = EngineRequest.builder()
                .clientVersion("2")
                .build();
        ExecutorContext executorContext = ExecutorContext.builder()
                .request(request)
                .build();

        boolean trueRes = condition.innerExecute(executorContext, null);
        log.info("testNumberLe trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        boolean falseRes = condition.innerExecute(executorContext.toBuilder()
                        .request(request.toBuilder().clientVersion("3").build())
                        .build()
                , null);
        log.info("testNumberLe falseRes:{}", falseRes);
        Assert.assertFalse(falseRes);

        boolean smallRes = condition.innerExecute(executorContext.toBuilder()
                        .request(request.toBuilder().clientVersion("1").build())
                        .build()
                , null);
        log.info("testNumberLe smallRes:{}", smallRes);
        Assert.assertTrue(smallRes);

        //请求版本号为空
        boolean emptyRes = condition.innerExecute(executorContext.toBuilder()
                        .request(request.toBuilder().clientVersion("").build())
                        .build()
                , null);
        log.info("testNumberLe emptyRes:{}", emptyRes);
        Assert.assertFalse(emptyRes);
    }

    @Test
    public void testNumberGt(){
        //要求版本号大于2
        ConditionDTO conditionDTO = new ConditionDTO();
        conditionDTO.setAttribute(clientVersionKey);
        conditionDTO.setOperateSymbol(OperateSymbol.GT.getValue());
        conditionDTO.setValue(2);
        NumberGtCondition condition = new NumberGtCondition(conditionDTO);

        EngineRequest request = EngineRequest.builder()
                .clientVersion("3")
                .build();

        ExecutorContext executorContext = ExecutorContext.builder()
                .request(request)
                .build();

        //符合条件情况
        boolean trueRes = condition.innerExecute(executorContext, null);
        log.info("testNumberGt trueRes:{}", trueRes);
        Assert.assertTrue(trueRes);

        //不符合条件的情况
        boolean falseRes = condition.innerExecute(executorContext.toBuilder()
                        .request(request.toBuilder().clientVersion("1").build())
                        .build()
                , null);
        log.info("testNumberGt falseRes:{}", falseRes);
        Assert.assertFalse(falseRes);

        //刚好等于的情况
        boolean equalRes = condition.innerExecute(executorContext.toBuilder()
                        .request(request.toBuilder().clientVersion("2").build())
                        .build()
                , null);
        log.info("testNumberGt equalRes:{}", equalRes);
        Assert.assertFalse(equalRes);

        boolean emptyRes = condition.innerExecute(executorContext.toBuilder()
                        .request(request.toBuilder().clientVersion("").build())
                        .build()
                , null);
        log.info("testNumberGt emptyRes:{}", emptyRes);
        Assert.assertFalse(emptyRes);
    }

}
