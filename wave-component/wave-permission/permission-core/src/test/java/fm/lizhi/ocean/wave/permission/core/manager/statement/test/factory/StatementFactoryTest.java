package fm.lizhi.ocean.wave.permission.core.manager.statement.test.factory;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.Resource;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.StatementFacade;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.StatementResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.ResourceType;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.StatementFactory;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser.BeanExecutorParser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser.InParser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser.InUserGroupParser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser.StringEqParser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser.number.*;
import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/26 16:16
 */
public class StatementFactoryTest {

    private StatementFactory statementFactory = new StatementFactory(Lists.newArrayList(
            new StringEqParser()
            , new BeanExecutorParser(null)
            , new InUserGroupParser(null)
            , new NumberEqParser()
            , new NumberLeParser()
            , new NumberGeParser()
            , new NumberLtParser()
            , new NumberGtParser()
            , new InParser()
    ));

    @Test
    public void test() {
        String dsl = "{\n" +
                "    \"id\": 1,\n" +
                "    \"cacheStrategy\": {\n" +
                "        \"enable\": true,\n" +
                "        \"unit\": \"user.userId\",\n" +
                "        \"expireSeconds\": 60\n" +
                "    },\n" +
                "    \"effect\": \"ALLOW\", \n" +
                "    \"resources\": [\n" +
                "        {\n" +
                "            \"type\": \"permissions\",\n" +
                "            \"value\": [\n" +
                "                \"operator_live\"\n" +
                "            ] \n" +
                "        }\n" +
                "    ],\n" +
                "    \"conditionGroup\": {\n" +
                "        \"conditionList\": [\n" +
                "            {\n" +
                "                \"attribute\": \"request.clientVersion\", \n" +
                "                \"operateSymbol\": \"GT\", \n" +
                "                \"value\": \"1111\" \n" +
                "            },\n" +
                "            {\n" +
                "                \"attribute\": \"request.appId\", \n" +
                "                \"operateSymbol\": \"IN\", \n" +
                "                \"value\": [\n" +
                "                    \"9637128\"\n" +
                "                ] \n" +
                "            },\n" +
                "            {\n" +
                "                \"attribute\": \"live.liveModel\",\n" +
                "                \"operateSymbol\": \"IN\",\n" +
                "                \"value\": [\n" +
                "                    \"1\"\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"attribute\": \"user.userId\",\n" +
                "                \"operateSymbol\": \"IN_USERGROUP\",\n" +
                "                \"value\": \"1111\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"logicSymbol\": \"OR\",\n" +
                "        \"conditionGroup\": {}\n" +
                "    }\n" +
                "}";
        StatementFacade statement = statementFactory.createStatement(dsl);
        System.out.println("statement = " + statement);

    }

    @Test
    public void testResult() {

        StatementResult allowResult = StatementResult.builder()
                .allowResource(Resource.builder()
                        .type(ResourceType.ROLE)
                        .value(Lists.newArrayList("1", "2"))
                        .build()
                )
                .allowResource(Resource.builder()
                        .type(ResourceType.ROLE)
                        .value(Lists.newArrayList("1", "2", "3"))
                        .build()
                )
                .allowResource(Resource.builder()
                        .type(ResourceType.PERMISSION)
                        .value(Lists.newArrayList("1", "2", "3"))
                        .build()
                )
                .allowResource(Resource.builder()
                        .type(ResourceType.PERMISSION)
                        .value(Lists.newArrayList("4", "5"))
                        .build()
                )
                .build();
        List<Resource> resources = allowResult.mergeResources();
        List<String> role = allowResult.mergeResources(ResourceType.ROLE);
        List<String> permission = allowResult.mergeResources(ResourceType.PERMISSION);


        StatementResult denyResult = StatementResult.builder()
                .allowResource(Resource.builder()
                        .type(ResourceType.ROLE)
                        .value(Lists.newArrayList("1", "2", "3"))
                        .build()
                )
                .denyResource(Resource.builder()
                        .type(ResourceType.ROLE)
                        .value(Lists.newArrayList("2"))
                        .build()
                )
                .allowResource(Resource.builder()
                        .type(ResourceType.PERMISSION)
                        .value(Lists.newArrayList("4", "5"))
                        .build()
                )
                .build();
        List<Resource> resources1 = denyResult.mergeResources();
        List<String> denyRole = denyResult.mergeResources(ResourceType.ROLE);
        List<String> denyPermission = denyResult.mergeResources(ResourceType.PERMISSION);
        System.out.println("denyPermission = " + denyPermission);

    }

}
