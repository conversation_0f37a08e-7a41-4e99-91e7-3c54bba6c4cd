package fm.lizhi.ocean.wave.permission.core.extension.role.filter.page;

import com.alibaba.fastjson.JSON;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 直播打卡、麦序福利表页面权限过滤器
 * <AUTHOR>
 */
@Slf4j
@Component("module_live_check_in")
public class LiveCheckInPermissionFilter extends AbstractPagePermissionFilter {


    @Autowired
    private FamilyService familyService;
    
    @Autowired
    private PermissionConfig permissionConfig;

    @Override
    public boolean businessEnvSupport(BusinessEvnEnum app) {
        return BusinessEvnEnum.HEI_YE.equals(app) || BusinessEvnEnum.PP.equals(app) || BusinessEvnEnum.XIMI.equals(app);
    }

    @Override
    public boolean hasPermission(PermissionFilterDto filterDto) {
        try {
            // 版本限制
            int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
            if (permissionConfig.getCheckInV2MinimumVersion() > 0
                    && permissionConfig.getCheckInV2MinimumVersion() > clientVersion
                    && permissionConfig.getCheckInV1DisabledTime() != null
                    && permissionConfig.getCheckInV1DisabledTime().toInstant().toEpochMilli() <= System.currentTimeMillis()) {
                return false;
            }

            return checkFamilyPermission(filterDto);
        } catch (Exception e) {
            log.error("LiveCheckInPermissionFilter happen error: dto={}", JSON.toJSONString(filterDto), e);
            //报错了不显示这个权限
            return false;
        }
    }

    /**
     * 校验家族权限
     */
    private boolean checkFamilyPermission(PermissionFilterDto filterDto) {

        // 未开启家族权限判断
        if (!permissionConfig.getEnableCheckInFamilyPgcPermission()){
            return true;
        }

        // 获取签约厅厅主
        Result<Long> signNjId = familyService.playerCurSignNj(filterDto.getUserId());
        if (RpcResult.isFail(signNjId)) {
            log.info("LiveCheckInPermissionFilter.getUserInFamily fail, userId:{}", filterDto.getUserId());
            return false;
        }

        Long njId = filterDto.getNjId();

        // 是否是 PGC 厅
        Result<Boolean> pgcResult = familyService.checkUserInPGC(njId);
        if (RpcResult.isFail(pgcResult)) {
            // 失败，默认不放行
            log.info("LiveCheckInPermissionFilter.checkUserInPGC no pgc, njId:{}, rCode:{}", njId, pgcResult.rCode());
            return false;
        }

        log.info("LiveCheckInPermissionFilter.checkFamilyPermission. userId:{}, njId:{}, isPGC:{}, signNjId:{}",
                filterDto.getUserId(), njId, pgcResult.target(), signNjId.target());
        return // 是 PGC 厅
                pgcResult.target()
               // 是本厅签约主播 || 是房主
                && (signNjId.target().equals(njId) || njId.equals(filterDto.getUserId()));
    }
}
