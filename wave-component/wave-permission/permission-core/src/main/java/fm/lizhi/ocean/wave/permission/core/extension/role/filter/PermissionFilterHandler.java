package fm.lizhi.ocean.wave.permission.core.extension.role.filter;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.permission.core.constants.RolePermissionEnum;
import fm.lizhi.ocean.wave.permission.core.extension.role.PermissionFilter;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PermissionFilterHandler {


    private final Map<String, PermissionFilter> filters;

    public PermissionFilterHandler(PermissionFilterFactory permissionFilterFactory) {
        this.filters = permissionFilterFactory.get();
    }
    private Boolean isSupport(BusinessEvnEnum businessEvnEnum, PermissionFilterDto filterParam) {
        if (null == filterParam || null == filterParam.getLiveId() || null == filterParam.getUserId()){
            return false;
        }
        String permission = filterParam.getPermission();
        RolePermissionEnum permissionType = filterParam.getPermissionType();
        return MapUtils.isNotEmpty(filters)
                && filters.containsKey(permission)
                && filters.get(permission).isSupport(businessEvnEnum, permissionType)
                ;
    }


    public Boolean filter(BusinessEvnEnum businessEvnEnum, PermissionFilterDto filterParam){
        try {
            // 不支持的话，不过滤
            String permission = filterParam.getPermission();
            if (!isSupport(businessEvnEnum, filterParam)){
                return true;
            }

            // isSupport 已判空
            return filters.get(permission).hasPermission(filterParam);
        }catch (Exception e){
            log.error("PermissionFilterHandler filter error. businessEvnEnum: {}, permission:{}, liveId:{}",
                    businessEvnEnum, filterParam.getPermission(), filterParam.getLiveId(), e);
            return true;
        }

    }

}
