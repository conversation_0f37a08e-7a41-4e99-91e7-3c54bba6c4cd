package fm.lizhi.ocean.wave.permission.core.extension.role.filter.operation;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.param.GetLiveModeRequestParam;
import fm.lizhi.ocean.wave.api.live.result.GetLiveModeResult;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 蒙面礼物操作权限
 *
 * <AUTHOR>
 */
@Slf4j
@Component("operation_mask")
public class MaskPermissionFilter extends AbstractOperationPermissionFilter {

    @Autowired
    private LiveService liveService;

    @Autowired
    private PermissionConfig config;

    @Override
    public boolean businessEnvSupport(BusinessEvnEnum app) {
        return BusinessEvnEnum.PP.equals(app);
    }

    @Override
    public boolean hasPermission(PermissionFilterDto filterDto) {
        try {
            //版本校验，蒙面礼物只有PP有，获取配置直接读取PP的配置，后续有多业务，再抽取公共部分
            int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
            if (config.getPp().getMaskMinVersion() > 0 && clientVersion < config.getPp().getMaskMinVersion()) {
                return false;
            }

            log.info("MaskPermissionFilter.filter, liveId: {}", filterDto.getLiveId());
            if (CollectionUtils.isEmpty(config.getPp().getHasMaskLiveModes())) {
                //直播模式非空才校验
                return true;
            }

            long liveId = filterDto.getLiveId();
            Result<GetLiveModeResult> result = liveService.getLiveMode(GetLiveModeRequestParam.builder().liveId(liveId).build());
            int rCode = result.rCode();
            if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.error("MaskPermissionFilter.filter but get live mode error, liveId: {}, rCode: {}", liveId, rCode);
                return false;
            }

            // 判断是否在配置的直播模式中
            return config.getPp().getHasMaskLiveModes() != null && config.getPp().getHasMaskLiveModes().contains(result.target().getLiveModeEnum().getLiveMode());
        } catch (NumberFormatException e) {
            log.error("MaskPermissionFilter happen error: liveId:{}", filterDto.getLiveId(), e);
            //报错了不显示这个权限
            return false;
        }
    }
}
