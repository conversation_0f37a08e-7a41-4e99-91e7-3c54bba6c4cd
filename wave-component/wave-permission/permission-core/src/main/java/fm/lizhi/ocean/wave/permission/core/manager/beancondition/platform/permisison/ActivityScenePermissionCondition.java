package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.permisison;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import fm.lizhi.ocean.wave.permission.core.remote.IActivitySceneServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 活动现场权限检查
 */
@Slf4j
@Component
public class ActivityScenePermissionCondition extends AbstractBeanCondition {

    @MyAutowired
    private IActivitySceneServiceRemote activitySceneServiceRemote;


    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long liveId = context.getLive().getLiveId();
        Long userId = context.getUser().getUserId();

        if (liveId == null || liveId <= 0L || userId == null || userId <= 0L) {
            log.info("ActivityScenePermissionCondition params error. live_id={} user_id={}", liveId, userId);
            return false;
        }

        return activitySceneServiceRemote.checkPermission(liveId, userId);
    }

    @Override
    protected List<String> getCacheKeyItem(ExecutorContext context) {
        Long liveId = context.getLive().getLiveId();
        Long userId = context.getUser().getUserId();

        if (liveId == null || userId == null) {
            return Collections.emptyList();
        }

        return Lists.newArrayList(String.valueOf(liveId), String.valueOf(userId));
    }
}
