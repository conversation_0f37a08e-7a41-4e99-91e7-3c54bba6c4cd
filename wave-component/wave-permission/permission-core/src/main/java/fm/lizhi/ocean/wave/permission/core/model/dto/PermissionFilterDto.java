package fm.lizhi.ocean.wave.permission.core.model.dto;

import fm.lizhi.ocean.wave.permission.core.constants.RolePermissionEnum;
import fm.lizhi.ocean.wave.permission.core.datastore.entity.WavePermission;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PermissionFilterDto {

    /**
     * 权限英文
     * @see WavePermission#getPermission()
     */
    private String permission;

    private RolePermissionEnum permissionType;

    private Long liveId;

    private Long userId;

    private Long njId;

    private Long liveRoomId;
    private Integer appId;
}
