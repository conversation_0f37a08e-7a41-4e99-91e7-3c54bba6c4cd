package fm.lizhi.ocean.wave.permission.core.manager.statement;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/14 16:38
 */
@Getter
@Builder(toBuilder = true)
public class EngineRequest {

    private String clientVersion;

    private Integer appId;

    /**
     * 服务环境
     * pre=预发  pro=线上
     */
    private String env;

    /**
     * 请求时间戳
     */
    private Long requestTime;

}
