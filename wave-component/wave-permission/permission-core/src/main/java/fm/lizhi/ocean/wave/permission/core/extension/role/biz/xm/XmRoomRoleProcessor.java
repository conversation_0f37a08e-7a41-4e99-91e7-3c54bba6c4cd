package fm.lizhi.ocean.wave.permission.core.extension.role.biz.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.api.live.bean.LiveRoomUserRole;
import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import fm.lizhi.ocean.wave.permission.core.extension.role.IRoomRoleProcessor;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * xm房间角色权限差异化处理器
 */
@Slf4j
@Component
public class XmRoomRoleProcessor implements IRoomRoleProcessor {

    @Autowired
    private LiveRoomRoleService liveRoomRoleService;
    @Autowired
    private PermissionConfig permissionConfig;

    /**
     * 角色状态：正常
     */
    private final int ROLE_STATUS_NORMAL = 0;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public boolean superMangerPreCheck(ExecutorContext context) {
        return true;
    }

    @Override
    public boolean isPgcFamily(String familyType) {
        return "PGC".equals(familyType);
    }

    @Override
    public boolean hasSuperManager(ExecutorContext context) {
        //获取业务管理员角色ID
        int roleId = permissionConfig.getBusinessRoleId(PlatformRoleEnum.ROLE_SUPER_MANAGER.getRoleId(), context.getRequest().getAppId());
        Long roomId = context.getLive().getRoomId();
        Long userId = context.getUser().getUserId();

        if (roomId == null || roleId <= 0) {
            log.warn("param not validate, userId:{}, roomId:{}, roleId:{}, roomId is invalid", userId, roomId, roleId);
            return false;
        }

        Result<LiveRoomUserRole> liveRoomUserRoleResult = liveRoomRoleService.getLiveRoomUserRole(roomId, userId, roleId);
        if (liveRoomUserRoleResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("XmRoomRoleProcessor error, userId:{}, roomId:{}, roleId:{}, rCode:{}",
                    userId, roomId, roleId, liveRoomUserRoleResult.rCode());
            return false;
        }

        LiveRoomUserRole liveRoomUserRole = liveRoomUserRoleResult.target();
        boolean haveRoleInfo = liveRoomUserRoleResult.target() != null;
        log.info("XmRoomRoleProcessor success, userId:{}, roomId:{}, roleId:{}, has live room role:{},role status:{}",
                userId, roomId, roleId, haveRoleInfo, haveRoleInfo ? liveRoomUserRole.getStatus() : -1);

        return haveRoleInfo && liveRoomUserRole.getStatus() == ROLE_STATUS_NORMAL;
    }

    @Override
    public List<Integer> roleVoListFilter(List<Integer> roleIds) {
        return roleIds;
    }
}
