package fm.lizhi.ocean.wave.permission.core.extension.role.biz.hy;

import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.permission.core.extension.role.IVotePermissionProcessor;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description: HY投票权限差异化处理器
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2023/08/23 16:19
 */
@Slf4j
@Component
public class HyVotePermissionProcessor implements IVotePermissionProcessor {

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.HY;
    }

    @Override
    public boolean hasPermission(PermissionFilterDto filterParam) {

        return true;
    }
}
