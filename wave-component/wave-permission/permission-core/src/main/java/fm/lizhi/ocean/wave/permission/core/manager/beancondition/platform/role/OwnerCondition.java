package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.role;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomService;
import fm.lizhi.ocean.wave.api.live.result.GetLiveRoomResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 房主条件
 * <AUTHOR>
 * @date 2025/2/27 15:15
 */
@Slf4j
@Component
public class OwnerCondition extends AbstractBeanCondition {

    @Autowired
    private LiveRoomService liveRoomService;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long userId = context.getUser().getUserId();
        Long njId = context.getLive().getNjId();
        if (njId == null) {
            return false;
        }

        //基于角色判断的特殊性，直接调用底层的live接口获取数据做房主判断，不再引用live模块的api包
        Result<GetLiveRoomResult> getLiveRoomResult = liveRoomService.getLiveRoomByUserId(userId);
        if (getLiveRoomResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("OwnerCondition error, userId:{}, njId:{}, rCode:{}", userId, njId, getLiveRoomResult.rCode());
            return false;
        }

        log.info("OwnerCondition success, userId:{}, njId:{}", userId, njId);
        return getLiveRoomResult.target().getLiveRoom().getUserId() == njId;
    }

    @Override
    protected List<String> getCacheKeyItem(ExecutorContext context) {
        Long userId = context.getUser().getUserId();
        Long njId = context.getLive().getNjId();
        if (njId == null || userId == null) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(String.valueOf(userId), String.valueOf(njId));
    }
}
