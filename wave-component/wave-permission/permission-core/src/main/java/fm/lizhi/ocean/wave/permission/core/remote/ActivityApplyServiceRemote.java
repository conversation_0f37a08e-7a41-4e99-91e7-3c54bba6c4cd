package fm.lizhi.ocean.wave.permission.core.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.convert.ActivityApplyConvert;
import fm.lizhi.ocean.wave.permission.core.remote.bean.WaveActivityApplyToolBean;
import fm.lizhi.ocean.wave.permission.core.remote.request.GetInTimeRangeActivityApplyRequest;
import fm.lizhi.ocean.wave.server.common.constant.MsgCodes;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityApplyToolBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestGetInTimeRangeActivityApply;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/17 15:33
 */
@Component
public class ActivityApplyServiceRemote {

    @Autowired
    private ActivityApplyService activityApplyService;

    public Result<List<WaveActivityApplyToolBean>> getInTimeRangeActivityApply(GetInTimeRangeActivityApplyRequest request){
        Result<List<ActivityApplyToolBean>> result = activityApplyService.getInTimeRangeActivityApply(new RequestGetInTimeRangeActivityApply()
                .setAppId(request.getAppId())
                .setNjId(request.getNjId())
                .setStartTimeBeforeMinute(request.getStartTimeBeforeMinute())
                .setEndTimeAfterMinute(request.getEndTimeAfterMinute())
        );
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(MsgCodes.FAIL.getCode());
        }

        return RpcResult.success(ActivityApplyConvert.I.convertWaveActivityApplyToolBeans(result.target()));
    }

}
