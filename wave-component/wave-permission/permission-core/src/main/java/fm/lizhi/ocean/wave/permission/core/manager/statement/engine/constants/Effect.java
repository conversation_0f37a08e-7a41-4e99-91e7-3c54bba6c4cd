package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants;

/**
 * <AUTHOR>
 * @date 2025/1/13 19:59
 */
public enum Effect {

    /**
     * 允许
     */
    ALLOW("ALLOW"),

    /**
     * 拒绝
     */
    DENY("DENY");

    private final String value;

    Effect(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static Effect getByValue(String value) {
        for (Effect effect : Effect.values()) {
            if (effect.getValue().equals(value)) {
                return effect;
            }
        }
        return null;
    }

}
