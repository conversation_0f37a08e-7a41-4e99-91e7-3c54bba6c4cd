package fm.lizhi.ocean.wave.permission.core.extension.role;

import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;

import java.util.List;

/**
 * 房间角色权限差异化处理器
 */
public interface IRoomRoleProcessor extends BusinessEnvAwareProcessor {

    /**
     * 是否是公司家族
     *
     * @param familyType 家族类型
     * @return 是否是陪玩
     */
    boolean isPgcFamily(String familyType);


    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IRoomRoleProcessor.class;
    }

    /**
     * 超级管理员前置检查
     * @return
     */
    boolean superMangerPreCheck(ExecutorContext context);

    /**
     * 是否拥有超级管理员
     * @param context
     * @return
     */
    boolean hasSuperManager(ExecutorContext context);

    /**
     * 处理不同业务角色列表返回过滤
     * 比如 PP和黑叶 返回角色列表需要将主持人替换为管理员
     * 先直接过滤 后面看看有没有更好的设计
     * @param roleIds
     * @return
     */
    List<Integer> roleVoListFilter(List<Integer> roleIds);
}
