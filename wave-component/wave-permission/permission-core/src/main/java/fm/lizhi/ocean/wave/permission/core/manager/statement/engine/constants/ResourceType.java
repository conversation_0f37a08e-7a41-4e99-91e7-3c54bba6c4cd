package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/14 15:57
 */
public enum ResourceType {

    PERMISSION("permission")
    , ROLE("role")
    ;

    @Getter
    private String value;

    ResourceType(String value) {
        this.value = value;
    }

    public static ResourceType getByValue(String value) {
        for (ResourceType resourceType : ResourceType.values()) {
            if (resourceType.getValue().equals(value)) {
                return resourceType;
            }
        }
        return null;
    }
}
