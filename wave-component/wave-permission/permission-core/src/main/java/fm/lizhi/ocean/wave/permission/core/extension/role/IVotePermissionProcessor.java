package fm.lizhi.ocean.wave.permission.core.extension.role;

import fm.lizhi.ocean.wave.api.permission.param.PermissionCheckParam;
import fm.lizhi.ocean.wave.common.extension.IProcessor;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.permission.core.model.vo.RolePermissionVO;

/**
 * 投票权限个性化处理
 */
public interface IVotePermissionProcessor extends IProcessor<PermissionCheckParam, Void, RolePermissionVO, Void> {
    @Override
    default Class<? extends IProcessor> getBaseBusinessProcessor() {
        return IVotePermissionProcessor.class;
    }

    boolean hasPermission(PermissionFilterDto filterParam);
}
