package fm.lizhi.ocean.wave.permission.core.manager.statement;

import lombok.Builder;
import lombok.Getter;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 * @date 2025/2/14 16:39
 */
@Getter
@Builder(toBuilder = true)
public class EngineLive {

    @Nullable
    private Long liveId;

    @Nullable
    private Long roomId;

    @Nullable
    private Long njId;

    /**
     * 直播间模式
     * LiveModeEnum#name()
     */
    @Nullable
    private String liveModel;

}
