package fm.lizhi.ocean.wave.permission.core.extension.role.biz.xm;

import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.permission.core.constants.RolePermissionEnum;
import fm.lizhi.ocean.wave.permission.core.extension.role.IRolePermissionProcessor;
import fm.lizhi.ocean.wave.permission.core.extension.role.filter.PermissionFilterHandler;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.permission.core.model.vo.RolePermissionVO;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: XM角色权限差异化处理器
 * @author: chen<PERSON>han
 */
@Slf4j
@Component
public class XmRolePermissionProcessor implements IRolePermissionProcessor {


    @Autowired
    private PermissionFilterHandler permissionFilterHandler;

    @Override
    public ResultVO<Void> postprocessor(RolePermissionVO rolePremissionResult) {
        //目前前端不会展示，所以不处理
        return ResultVO.success();
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.XM;
    }


    @Override
    public RolePermissionVO filter(PermissionFilterDto filterParam, RolePermissionVO result) {

        List<String> modulesList = result.getModules().stream().filter(permission -> {
            filterParam.setPermissionType(RolePermissionEnum.ROLE_PAGE_PERMISSION_ENUM);
            filterParam.setPermission(permission);

            return permissionFilterHandler.filter(BusinessEvnEnum.XIMI, filterParam);
        }).collect(Collectors.toList());

        List<String> operationList = result.getOperations().stream().filter(permission -> {
            filterParam.setPermissionType(RolePermissionEnum.ROLE_OPERATION_PERMISSION_ENUM);
            filterParam.setPermission(permission);

            return permissionFilterHandler.filter(BusinessEvnEnum.XIMI, filterParam);
        }).collect(Collectors.toList());

        result.setModules(modulesList);
        result.setOperations(operationList);
        return result;
    }
}
