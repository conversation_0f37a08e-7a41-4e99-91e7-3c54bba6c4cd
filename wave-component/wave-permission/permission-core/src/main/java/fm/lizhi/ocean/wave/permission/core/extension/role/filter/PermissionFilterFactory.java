package fm.lizhi.ocean.wave.permission.core.extension.role.filter;

import fm.lizhi.ocean.wave.permission.core.extension.role.PermissionFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PermissionFilterFactory {


    private final Map<String, PermissionFilter> filters;

    public PermissionFilterFactory(Map<String, PermissionFilter> filters) {
        this.filters = filters;
    }


    /**
     * 目前只是简单的注入并获取
     */
    public Map<String, PermissionFilter> get() {
        return filters;
    }

}
