package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.permisison;

import api.activity.api.LivePkService;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * operation_pk权限对应条件
 * <AUTHOR>
 * @date 2025/2/27 16:41
 */
@Slf4j
@Component
public class OperationPkCondition extends AbstractBeanCondition {

    @Autowired
    private LivePkService livePkService;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long userId = context.getUser().getUserId();
        Long njId = context.getLive().getNjId();
        Long liveId = context.getLive().getLiveId();
        if (njId == null || liveId == null) {
            return false;
        }
        Result<Boolean> result = livePkService.checkUserPkAuthority(userId, liveId, njId);
        if (RpcResult.isFail(result)){
            // 默认不放行
            return false;
        }
        return result.target();
    }
}
