package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.permisison;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 直播打卡、麦序福利表页面权限条件
 * <AUTHOR>
 * @date 2025/2/28 16:36
 */
@Slf4j
@Component
public class LiveCheckInPermissionCondition extends AbstractBeanCondition {

    @Autowired
    private FamilyService familyService;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        // 版本限制
        int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
        if (permissionConfig.getCheckInV2MinimumVersion() > 0
                && permissionConfig.getCheckInV2MinimumVersion() > clientVersion
                && permissionConfig.getCheckInV1DisabledTime() != null
                && permissionConfig.getCheckInV1DisabledTime().toInstant().toEpochMilli() <= System.currentTimeMillis()) {
            return false;
        }

        return checkFamilyPermission(context);
    }

    /**
     * 校验家族权限
     */
    private boolean checkFamilyPermission(ExecutorContext context) {
        Long userId = context.getUser().getUserId();
        Long njId = context.getLive().getNjId();
        if (njId == null) {
            return false;
        }

        // 未开启家族权限判断
        if (!permissionConfig.getEnableCheckInFamilyPgcPermission()){
            return true;
        }

        // 获取签约厅厅主
        Result<Long> signNjId = familyService.playerCurSignNj(userId);
        if (RpcResult.isFail(signNjId)) {
            log.info("LiveCheckInPermissionCondition.getUserInFamily fail, userId:{}", userId);
            return false;
        }


        // 是否是 PGC 厅
        Result<Boolean> pgcResult = familyService.checkUserInPGC(njId);
        if (RpcResult.isFail(pgcResult)) {
            // 失败，默认不放行
            log.info("LiveCheckInPermissionCondition.checkUserInPGC no pgc, njId:{}, rCode:{}", njId, pgcResult.rCode());
            return false;
        }

        log.info("LiveCheckInPermissionCondition.checkFamilyPermission. userId:{}, njId:{}, isPGC:{}, signNjId:{}",
                userId, njId, pgcResult.target(), signNjId.target());
        return // 是 PGC 厅
                pgcResult.target()
                        // 是本厅签约主播 || 是房主
                        && (signNjId.target().equals(njId) || njId.equals(userId));
    }

}
