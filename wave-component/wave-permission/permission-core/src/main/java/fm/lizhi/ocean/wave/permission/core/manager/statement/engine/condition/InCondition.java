package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/26 15:36
 */
public class InCondition extends AbstractCondition<List<String>>{

    public InCondition(ConditionDTO statement) {
        super(statement);
        String jsonStr = JsonUtil.dumps(statement.getValue());
        this.value = JSON.parseArray(jsonStr, String.class);
    }

    @Override
    public boolean innerExecute(ExecutorContext context, IStatement statement) {
        Object attrValueStr = JSONPath.eval(context, genAttributeJsonPath());
        if (attrValueStr == null) {
            return false;
        }

        String str = String.valueOf(attrValueStr);
        if (StringUtils.isBlank(str)) {
            return false;
        }

        if (this.value == null) {
            return false;
        }

        return this.value.contains(str);
    }
}
