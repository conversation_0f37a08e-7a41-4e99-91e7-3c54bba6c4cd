package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition;

import com.alibaba.fastjson.JSONPath;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import fm.lizhi.ocean.wave.user.api.UserGroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * <AUTHOR>
 * @date 2025/2/26 16:01
 */
@Slf4j
public class NotInUserGroupCondition extends AbstractCondition<Long>{

    private UserGroupService userGroupService;

    public NotInUserGroupCondition(ConditionDTO statement, UserGroupService userGroupService) {
        super(statement);
        this.userGroupService = userGroupService;
        // 用户组ID
        super.value = Long.valueOf(String.valueOf(statement.getValue()));
    }

    @Override
    public boolean innerExecute(ExecutorContext context, IStatement statement) {
        Object attrValueObj = JSONPath.eval(context, genAttributeJsonPath());
        if (attrValueObj == null) {
            return false;
        }

        String attrValueStr = String.valueOf(attrValueObj);
        if (!NumberUtils.isCreatable(attrValueStr)) {
            return false;
        }

        long itemId = Long.parseLong(attrValueStr);
        Result<Boolean> result = userGroupService.isUserInGroup(super.value, itemId, 0);
        log.debug("notInUserGroupCondition rCode={},result={}", result.rCode(), result.target());
        if (RpcResult.isFail(result) || result.target() == null) {
            return false;
        }
        return !result.target();
    }
}
