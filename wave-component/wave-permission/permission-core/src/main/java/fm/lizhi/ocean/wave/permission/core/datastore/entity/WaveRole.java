package fm.lizhi.ocean.wave.permission.core.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 角色表
 *
 * @date 2023-05-22 06:11:22
 */
@Table(name = "`wave_role`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WaveRole {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Integer id;

    /**
     * 角色名称
     */
    @Column(name= "`role_name`")
    private String roleName;

    /**
     * 区分业务线
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 直播模式，默认娱乐厅
     */
    @Column(name= "`live_mode`")
    private Integer liveMode;

    /**
     * 角色创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 角色修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", roleName=").append(roleName);
        sb.append(", appId=").append(appId);
        sb.append(", liveMode=").append(liveMode);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}