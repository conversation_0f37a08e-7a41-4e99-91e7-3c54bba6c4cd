package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wave.common.manager.RedisClientManager;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import fm.lizhi.ocean.wave.permission.core.dao.redis.PermissionRedisKey;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * bean条件执行器基类
 * <AUTHOR>
 * @date 2025/2/14 16:54
 */
@Slf4j
public abstract class AbstractBeanCondition {

    @Autowired
    private RedisClientManager redisClientManager;
    @Autowired
    protected PermissionConfig permissionConfig;

    /**
     * 执行条件判断, 如果返回true, 则说明条件执行通过
     * @param context
     * @return
     */
    protected abstract boolean doExecute(ExecutorContext context, IStatement statement);

    /**
     * 缓存key参数
     * 获取需要缓存维度的值, 如果返回用户ID, 则使用用户ID作为key进行缓存
     * @return 空则不缓存
     */
    protected List<String> getCacheKeyItem(ExecutorContext context) {
        return null;
    }

    public boolean execute(ExecutorContext context, IStatement statement, IBeanExecutorValue executorValue) {
        try {
            //缓存
            int cacheSecond = executorValue.getCacheSecond();
            List<String> cacheKeyItem = getCacheKeyItem(context);
            if (cacheSecond <= 0 || cacheKeyItem == null || cacheKeyItem.isEmpty()) {
                // 缓存被取消或者没有缓存需求
                return doExecute(context, statement);
            }
            cacheKeyItem.add(String.valueOf(statement.getId()));

            //查缓存
            String key = PermissionRedisKey.ENGINE_BEAN_EXECUTOR.getKey(cacheKeyItem);
            String resultStr = getRedisClient().get(key);
            if (resultStr != null) {
                return Boolean.parseBoolean(resultStr);
            }

            boolean result = doExecute(context, statement);

            //缓存
            getRedisClient().setex(key
                    , Math.min(cacheSecond, permissionConfig.getPermissionEngineExecutorMaxCacheSeconds())
                    , Boolean.toString(result));
            return result;
        } catch (Exception e) {
            log.error("abstractBeanCondition class:{} error: ", getClass().getName(), e);
        }
        return false;
    }

    private RedisClient getRedisClient(){
        return redisClientManager.redisCacheClient();
    }

}
