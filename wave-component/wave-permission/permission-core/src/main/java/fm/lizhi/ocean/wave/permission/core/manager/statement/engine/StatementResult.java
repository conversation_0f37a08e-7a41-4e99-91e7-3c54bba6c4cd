package fm.lizhi.ocean.wave.permission.core.manager.statement.engine;

import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.ResourceType;
import lombok.Builder;
import lombok.Getter;
import lombok.Singular;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 策略执行结果
 * <AUTHOR>
 * @date 2025/2/17 19:15
 */
@Getter
@Builder
public class StatementResult {

    /**
     * 有效的资源
     */
    @Singular
    private List<Resource> allowResources;

    /**
     * 被拒绝的资源
     */
    @Singular
    private List<Resource> denyResources;

    /**
     * 合并资源
     * 返回最终有效的，过滤被拒绝的资源
     * @return 会返回新的列表对象
     */
    public List<Resource> mergeResources(){
        if (CollectionUtils.isEmpty(denyResources) && CollectionUtils.isEmpty(allowResources)) {
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(denyResources)) {
            return allowResources;
        }

        Map<ResourceType, List<String>> allowTypeMap = getTypeMap(this.allowResources);
        Map<ResourceType, List<String>> denyTypeMap = getTypeMap(this.denyResources);

        List<Resource> resultAllow = new ArrayList<>();

        for (Map.Entry<ResourceType, List<String>> entry : allowTypeMap.entrySet()) {
            ResourceType type = entry.getKey();
            List<String> valueList = denyTypeMap.get(type);
            if (CollectionUtils.isEmpty(valueList)) {
                //没有需要过滤的
                resultAllow.add(Resource.builder()
                        .type(type)
                        .value(entry.getValue())
                        .build());
                continue;
            }
            resultAllow.add(Resource.builder()
                    .type(type)
                    .value(entry.getValue().stream().filter(v->!valueList.contains(v)).collect(Collectors.toList()))
                    .build());
        }

        return resultAllow;
    }

    /**
     * 合并资源 并返回指定类型的值
     * @param type
     * @return
     */
    public List<String> mergeResources(ResourceType type){
        List<Resource> resources = mergeResources();
        return resources.stream()
                .filter(r->r.getType() == type)
                .flatMap(r->r.getValue().stream())
                .collect(Collectors.toList());
    }

    /**
     * 将资源同类型的值展开
     * @param resources
     * @return
     */
    private Map<ResourceType, List<String>> getTypeMap(List<Resource> resources){
        return resources.stream().collect(Collectors.groupingBy(
                Resource::getType,
                Collectors.collectingAndThen(Collectors.toList(),
                        list->list.stream()
                                .flatMap(r->r.getValue().stream())
                                .collect(Collectors.toList())
                )
        ));
    }

    public static class StatementResultBuilder {

        /**
         * 构建的时候，相同类型的值合并去重
         * @return
         */
        public StatementResult build() {
            List<Resource> distinctAllow = mergeResources(this.allowResources);
            List<Resource> distinctDeny = mergeResources(this.denyResources);
            return new StatementResult(distinctAllow, distinctDeny);
        }

        /**
         * 将资源列表中,相同类型的值合并去重
         * @param resources
         * @return
         */
        private List<Resource> mergeResources(List<Resource> resources) {
            List<Resource> result = new ArrayList<>();
            if (CollectionUtils.isEmpty(resources)) {
                return result;
            }

            if (CollectionUtils.isNotEmpty(resources)) {
                Map<ResourceType, List<Resource>> typeMap = resources.stream().collect(Collectors.groupingBy(Resource::getType));

                for (Map.Entry<ResourceType, List<Resource>> typeListEntry : typeMap.entrySet()) {
                    List<Resource> value = typeListEntry.getValue();
                    if (CollectionUtils.isEmpty(value)) {
                        continue;
                    }

                    Resource resource = value.get(0);
                    for (int i = 0; i < value.size(); i++) {
                        if (i == 0) {
                            continue;
                        }
                        resource.merge(value.get(i));
                    }
                    result.add(resource);
                }
            }
            return result;
        }
    }

}
