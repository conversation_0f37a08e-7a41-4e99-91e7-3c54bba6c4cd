package fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser;

import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.ConditionParser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.InUserGroupCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol;
import fm.lizhi.ocean.wave.user.api.UserGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/26 16:08
 */
@Component
public class InUserGroupParser implements ConditionParser {

    private UserGroupService userGroupService;

    @Autowired
    public InUserGroupParser(UserGroupService userGroupService) {
        this.userGroupService = userGroupService;
    }

    @Override
    public OperateSymbol getOperateSymbol() {
        return OperateSymbol.IN_USERGROUP;
    }

    @Override
    public AbstractCondition<?> parser(ConditionDTO statement) {
        return new InUserGroupCondition(statement, userGroupService);
    }
}
