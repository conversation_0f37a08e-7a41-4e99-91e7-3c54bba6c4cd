package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.permisison;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import fm.lizhi.ocean.wave.platform.api.live.param.CheckKickOutPermissionNotConfigReq;
import fm.lizhi.ocean.wave.platform.api.live.service.LiveKickOutService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/27 16:47
 */
@Slf4j
@Component
public class OperationSetKickOutBtnCondition extends AbstractBeanCondition {

    @Autowired
    private LiveKickOutService liveKickOutService;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long roomId = context.getLive().getRoomId();
        Long userId = context.getUser().getUserId();
        Long njId = context.getLive().getNjId();
        if (njId == null || roomId == null) {
            return false;
        }

        CheckKickOutPermissionNotConfigReq permissionReq = CheckKickOutPermissionNotConfigReq.builder()
                .liveRoomId(roomId)
                .njId(njId)
                .operateUserId(userId)
                .build();

        // 校验差异化权限
        Result<Boolean> result = liveKickOutService.checkKickOutPermissionNotConfig(permissionReq);
        if (RpcResult.isFail(result)){
            // 踢人权限，默认不放行
            return false;
        }
        return result.target();
    }
}
