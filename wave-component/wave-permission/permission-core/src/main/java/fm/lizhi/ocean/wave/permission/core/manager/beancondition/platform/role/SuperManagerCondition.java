package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.role;

import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.permission.core.extension.role.IRoomRoleProcessor;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 超级管理员角色条件
 * <AUTHOR>
 * @date 2025/2/27 15:52
 */
@Slf4j
@Component
public class SuperManagerCondition extends AbstractBeanCondition {

    @Autowired
    private ProcessorV2Factory factory;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long roomId = context.getLive().getRoomId();
        if (roomId == null) {
            return false;
        }

        IRoomRoleProcessor processor = factory.getProcessor(IRoomRoleProcessor.class);
        boolean preCheck = processor.superMangerPreCheck(context);
        if (!preCheck) {
            return false;
        }

        return processor.hasSuperManager(context);
    }

}
