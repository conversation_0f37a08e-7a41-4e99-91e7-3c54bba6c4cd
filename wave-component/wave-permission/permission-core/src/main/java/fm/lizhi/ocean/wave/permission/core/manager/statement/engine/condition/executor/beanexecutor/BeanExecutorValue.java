package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor;

import lombok.Builder;
import lombok.Getter;

/**
 * bean执行器右值结构体
 * <AUTHOR>
 * @date 2025/2/27 17:15
 */
@Getter
@Builder
public class BeanExecutorValue implements IBeanExecutorValue{

    /**
     * bean名称
     */
    private String name;

    /**
     * 缓存时间
     */
    private int cacheSecond;

}
