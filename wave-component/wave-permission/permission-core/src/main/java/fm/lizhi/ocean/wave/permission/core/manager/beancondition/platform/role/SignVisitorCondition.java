package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.role;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomService;
import fm.lizhi.ocean.wave.api.live.bean.LiveRoomUserRole;
import fm.lizhi.ocean.wave.api.live.result.GetLiveRoomResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import fm.lizhi.ocean.wave.user.result.UserFamilyInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 签约游客条件
 * <AUTHOR>
 * @date 2025/2/27 16:18
 */
@Slf4j
@Component
public class SignVisitorCondition extends AbstractBeanCondition {

    @Autowired
    private LiveRoomService liveRoomService;
    @Autowired
    private LiveRoomRoleService liveRoomRoleService;
    @Autowired
    private FamilyService familyService;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long roomId = context.getLive().getRoomId();
        Long njId = context.getLive().getNjId();
        Long userId = context.getUser().getUserId();

        if (roomId == null) {
            if (njId == null) {
                return false;
            }
            Result<GetLiveRoomResult> liveRoomRes = liveRoomService.getLiveRoomByUserId(njId);
            if (liveRoomRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS || liveRoomRes.target() == null) {
                return false;
            }
            roomId = liveRoomRes.target().getLiveRoom().getId();
        }

        //先查询在该房间的角色
        Result<List<LiveRoomUserRole>> result = liveRoomRoleService.getLiveRoomUserAllRole(roomId, userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS && result.rCode() != LiveRoomRoleService.GET_LIVE_ROOM_USER_ALL_ROLE_NOT_FOUND) {
            return false;
        }

        List<LiveRoomUserRole> liveRoomRoleList = result.target();
        //有数据说明是当前房间的角色
        if (CollectionUtils.isNotEmpty(liveRoomRoleList)) {
            log.info("SignVisitorCondition has room role, userId:{}, roomId:{}, njId:{}", userId, roomId, njId);
            return false;
        }

        //如果没有权限，查询是否是生产者，家族长或者签约陪玩
        Result<UserFamilyInfoResult> familyRes = familyService.getUserFamily(userId, false);
        if (familyRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return false;
        }

        UserFamilyInfoResult userFamilyInfo = familyRes.target();
        if (userFamilyInfo.isRoom()) {
            //如果是厅主，且不在当前的厅，说明是签约游客角色
            if (!Objects.equals(userId, njId)) {
                log.info("SignVisitorCondition has room role, userId:{}, roomId:{}, njId:{}", userId, roomId, njId);
                return true;
            }
        }

        //如果是陪玩， 查询签约的主播ID
        if (userFamilyInfo.isPlayer()) {
            Result<Long> playerSignRes = familyService.playerCurSignNj(userId);
            if (playerSignRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                return false;
            }
            //如果是签约陪玩，不在签约主播的直播间，则是说明是签约游客角色
            if (!Objects.equals(playerSignRes.target(), njId)) {
                log.info("SignVisitorCondition has player role, userId:{}, roomId:{}, njId:{}", userId, roomId, njId);
                return true;
            }
        }

        //判断是否是家族或者陪玩
        if (userFamilyInfo.isFamily()) {
            //如果是家族长，判断当前签约的主播的直播间是否包含了该主播
            Result<List<Long>> familyNjRes = familyService.getFamilySignNjIds(userId, 0L);
            if (familyNjRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                return false;
            }

            List<Long> njIds = familyNjRes.target();
            //如果是家族长，且不在签约厅主的直播间，则是说明是签约游客角色
            if (!njIds.contains(njId)) {
                log.info("SignVisitorCondition has family role, userId:{}, roomId:{}, njId:{}", userId, roomId, njId);
                return true;
            }
        }

        return false;
    }

}
