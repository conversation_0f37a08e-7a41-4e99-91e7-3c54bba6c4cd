package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants;

/**
 * 逻辑符号
 * <AUTHOR>
 * @date 2025/2/14 16:37
 */
public enum LogicSymbol {

    AND("AND", "&&"),

    OR("OR", "||");

    private final String value;

    private final String symbol;

    LogicSymbol(String value, String symbol) {
        this.value = value;
        this.symbol = symbol;
    }

    public String getValue() {
        return value;
    }

    public String getSymbol() {
        return symbol;
    }

    public static LogicSymbol getByValue(String value) {
        for (LogicSymbol logicSymbol : LogicSymbol.values()) {
            if (logicSymbol.getValue().equals(value)) {
                return logicSymbol;
            }
        }
        return null;
    }
}
