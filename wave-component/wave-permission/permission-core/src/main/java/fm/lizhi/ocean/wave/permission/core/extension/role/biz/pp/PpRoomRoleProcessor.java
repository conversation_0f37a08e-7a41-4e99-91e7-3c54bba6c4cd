package fm.lizhi.ocean.wave.permission.core.extension.role.biz.pp;

import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.permission.core.extension.role.IRoomRoleProcessor;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * pp房间角色权限差异化处理器
 */
@Component
public class PpRoomRoleProcessor implements IRoomRoleProcessor {
    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public boolean isPgcFamily(String familyType) {
        return "C_FAMILY".equals(familyType);
    }
    @Override
    public boolean superMangerPreCheck(ExecutorContext context) {
        //没有超级管理员角色
        return false;
    }

    @Override
    public boolean hasSuperManager(ExecutorContext context) {
        return false;
    }

    @Override
    public List<Integer> roleVoListFilter(List<Integer> roleIds) {
        List<Integer> result = new ArrayList<>();
        for (Integer roleId : roleIds) {
            if (PlatformRoleEnum.ROLE_HOST.getRoleId() == roleId) {
                // 主持替换为管理员
                result.add(PlatformRoleEnum.ROLE_MANAGER.getRoleId());
            } else {
                result.add(roleId);
            }
        }
        return result;
    }
}
