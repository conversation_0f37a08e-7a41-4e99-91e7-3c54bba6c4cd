package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.role;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 主持人条件
 * <AUTHOR>
 * @date 2025/2/27 15:40
 */
@Slf4j
@Component
public class HostCondition extends AbstractBeanCondition {

    @Autowired
    private LiveRoomRoleService liveRoomRoleService;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long njId = context.getLive().getNjId();
        Long userId = context.getUser().getUserId();
        if (njId == null) {
            return false;
        }

        Result<Boolean> roomHostRes = liveRoomRoleService.isRoomHost(njId, userId);
        if (roomHostRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("HostCondition error, userId:{}, njId:{}, rCode:{}", userId, njId, roomHostRes.rCode());
            return false;
        }

        Boolean isRoomHost = roomHostRes.target();
        log.info("HostCondition invoke success, userId:{}, njId:{}, isRoomHost:{}", userId, njId, isRoomHost);
        return isRoomHost;
    }

}
