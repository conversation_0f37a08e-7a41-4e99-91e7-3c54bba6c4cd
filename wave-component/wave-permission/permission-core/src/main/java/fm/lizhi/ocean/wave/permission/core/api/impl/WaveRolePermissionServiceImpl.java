package fm.lizhi.ocean.wave.permission.core.api.impl;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.permission.api.WaveRolePermissionService;
import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.api.permission.param.PermissionCheckParam;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.permission.core.constants.PermissionCodeEnum;
import fm.lizhi.ocean.wave.permission.core.manager.PlatformRoleManager;
import fm.lizhi.ocean.wave.permission.core.manager.RoomRoleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 角色权限服务实现
 * @author: guoyibin
 * @create: 2023/05/20 11:59
 */
@Slf4j
@Component
public class WaveRolePermissionServiceImpl implements WaveRolePermissionService {

    @Autowired
    private PlatformRoleManager platformRoleManager;

    @Autowired
    private RoomRoleManager roomRoleManager;

    @Override
    public Boolean havePermission(PermissionCheckParam checkParam) {
        if (checkParam.getPlatformRoleId() <= 0 || checkParam.getAppId() <= 0
                || checkParam.getUserId() <= 0 || checkParam.getRoomId() <= 0
                || checkParam.getNjId() <= 0) {
            log.warn("havePermission.param: param is not validate, param:{}", JsonUtils.toJsonString(checkParam));
            return false;
        }

        return platformRoleManager.havePermission(checkParam);
    }

    @Override
    public Result<Map<PlatformRoleEnum, Boolean>> batchGetPermission(long userId, long njId, long roomId, int appId, List<PlatformRoleEnum> platformRoleEnums) {
        boolean havaPermissionResult = false;
        Map<PlatformRoleEnum, Boolean> platformRoleMap = new HashMap<>();
        for (PlatformRoleEnum platformRoleEnum : platformRoleEnums) {
            Boolean havePermission = this.havePermission(PermissionCheckParam.builder()
                    .userId(userId)
                    .njId(njId)
                    .roomId(roomId)
                    .appId(appId)
                    .platformRoleId(platformRoleEnum.getRoleId())
                    .build());
            if (!havaPermissionResult) {
                havaPermissionResult = havePermission;
            }
            platformRoleMap.put(platformRoleEnum, havePermission);
        }
        if (!havaPermissionResult) {
            return new Result<>(WaveRolePermissionService.BATCH_GET_PERMISSION_NO_AUTH, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, platformRoleMap);
    }

    @Override
    public Result<Void> haveInRoomRole(long userId, long njId, long roomId) {
        Result<Void> result = roomRoleManager.haveInRoomRole(userId, roomId, njId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("haveInRoomRole.param: param is not validate, userId:{}, njId:{}, roomId:{}, rCode={}", userId, njId, roomId, result.rCode());
            int code = result.rCode() == PermissionCodeEnum.IN_ROOM_ROLE_NO_ROLE_ERROR.getCode()
                    ? WaveRolePermissionService.HAVE_IN_ROOM_ROLE_NO_AUTH : WaveRolePermissionService.HAVE_IN_ROOM_ROLE_FAIL;
            return new Result<>(code, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

}
