package fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser.number;

import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.ConditionParser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.number.NumberEqCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/26 15:15
 */
@Component
public class NumberEqParser implements ConditionParser {

    @Override
    public OperateSymbol getOperateSymbol() {
        return OperateSymbol.NUMBER_EQ;
    }

    @Override
    public AbstractCondition<?> parser(ConditionDTO statement) {
        return new NumberEqCondition(statement);
    }
}
