package fm.lizhi.ocean.wave.permission.core.manager.statement.engine;

import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.LogicSymbol;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 20:08
 */
@Slf4j
@Getter
@Builder
public class ConditionGroup implements InnerExecutorObject{
    private List<AbstractCondition<?>> conditionList;

    private LogicSymbol logicSymbol;

    private ConditionGroup conditionGroup;

    @Override
    public boolean innerExecute(ExecutorContext context, IStatement statement) {
        if (CollectionUtils.isEmpty(conditionList)) {
            // 条件为空，直接返回false
            return false;
        }

        // AND初始为true，OR初始为false
        boolean finalResult = logicSymbol == LogicSymbol.AND;

        for (int i = 0; i < conditionList.size(); i++) {
            AbstractCondition<?> condition = conditionList.get(i);
            boolean currentResult = condition.innerExecute(context, statement);
            log.debug("condition execute statementId={},i={},currentResult={}", statement.getId(), i, currentResult);

            // AND逻辑遇到false立即返回
            if (logicSymbol == LogicSymbol.AND && !currentResult) {
                return false;
            }
            // OR逻辑遇到true立即返回
            if (logicSymbol == LogicSymbol.OR && currentResult) {
                return true;
            }

            finalResult = (logicSymbol == LogicSymbol.AND) ?
                    (finalResult && currentResult) :
                    (finalResult || currentResult);
        }

        // 处理conditionGroup（若有）
        if (conditionGroup != null && CollectionUtils.isNotEmpty(conditionGroup.getConditionList())) {
            boolean groupResult = conditionGroup.innerExecute(context, statement);
            finalResult = (logicSymbol == LogicSymbol.AND) ?
                    (finalResult && groupResult) :
                    (finalResult || groupResult);
        }

        return finalResult;
    }

}
