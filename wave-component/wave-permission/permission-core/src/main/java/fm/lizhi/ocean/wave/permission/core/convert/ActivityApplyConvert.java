package fm.lizhi.ocean.wave.permission.core.convert;

import fm.lizhi.ocean.wave.permission.core.remote.bean.WaveActivityApplyToolBean;
import fm.lizhi.ocean.wave.permission.core.remote.bean.WaveActivityToolBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityApplyToolBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolBean;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/17 15:47
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityApplyConvert {

    ActivityApplyConvert I = Mappers.getMapper(ActivityApplyConvert.class);

    WaveActivityApplyToolBean convertWaveActivityApplyToolBean(ActivityApplyToolBean bean);

    List<WaveActivityApplyToolBean> convertWaveActivityApplyToolBeans(List<ActivityApplyToolBean> beans);

    WaveActivityToolBean convertWaveActivityToolBean(ActivityToolBean bean);

    List<WaveActivityToolBean> convertWaveActivityToolBeans(List<ActivityToolBean> beans);


}
