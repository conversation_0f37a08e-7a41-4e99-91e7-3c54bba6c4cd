package fm.lizhi.ocean.wave.permission.core.remote.request;

import fm.lizhi.ocean.wave.common.util.WaveAssert;
import fm.lizhi.ocean.wave.server.common.constant.MsgCodes;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/17 15:38
 */
@Getter
@Builder
public class GetInTimeRangeActivityApplyRequest {

    private Integer appId;

    private Long njId;

    /**
     * 开始时间前分钟数
     */
    private Integer startTimeBeforeMinute;

    /**
     * 结束时间往后的分钟数
     */
    private Integer endTimeAfterMinute;

    public static class GetInTimeRangeActivityApplyRequestBuilder {
        public GetInTimeRangeActivityApplyRequest build() {
            WaveAssert.notNull(appId, MsgCodes.PARAM_ERROR);
            WaveAssert.notNull(njId, MsgCodes.PARAM_ERROR);
            WaveAssert.notNull(startTimeBeforeMinute, MsgCodes.PARAM_ERROR);
            WaveAssert.notNull(endTimeAfterMinute, MsgCodes.PARAM_ERROR);
            return new GetInTimeRangeActivityApplyRequest(appId, njId, startTimeBeforeMinute, endTimeAfterMinute);
        }
    }

}
