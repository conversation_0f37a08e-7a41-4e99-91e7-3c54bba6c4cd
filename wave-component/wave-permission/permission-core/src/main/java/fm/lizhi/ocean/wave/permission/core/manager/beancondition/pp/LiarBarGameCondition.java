package fm.lizhi.ocean.wave.permission.core.manager.beancondition.pp;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomService;
import fm.lizhi.ocean.wave.api.live.result.GetLiveRoomCategoryResult;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 小游戏骗子酒馆权限
 *
 * <AUTHOR>
 * @date 2025/4/17 10:31
 */
@Slf4j
@Component
public class LiarBarGameCondition extends AbstractBeanCondition {

    @Autowired
    private PermissionConfig permissionConfig;

    @Autowired
    private LiveRoomService liveRoomService;


    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long njId = context.getLive().getNjId();
        if (njId == null) {
            log.info("njId is null");
            return false;
        }

        //骗子酒馆的只有在UGC，还有个播的才显示
        List<GetLiveRoomCategoryResult.Category> liveRoomCategory = getLiveRoomCategory(njId);
        if (CollectionUtils.isEmpty(liveRoomCategory)) {
            //表示UGC
            return true;
        }
        Optional<GetLiveRoomCategoryResult.Category> optional = liveRoomCategory.stream()
                .filter(c -> permissionConfig.getPp().getPgcCategoryIds().contains(c.getId() + ""))//如果包含PGC分类的就是PGC了
                .findFirst();
        if (!optional.isPresent()) {
            //表示UGC
            return true;
        }
        //包含个播
        return liveRoomCategory.stream().anyMatch(s -> permissionConfig.getPp().getPersonalLiveCategoryId() == s.getId());
    }

    /**
     * /**
     * 获取直播分类
     *
     * @param userId 房主id
     * @return
     */
    public List<GetLiveRoomCategoryResult.Category> getLiveRoomCategory(long userId) {
        Result<GetLiveRoomCategoryResult> result = liveRoomService.getLiveRoomCategoryByLocalCache(userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getLiveRoomCategory failed, userId={}`rCode={}", userId, result.rCode());
            return new ArrayList<>();
        }
        return result.target().getCategoryList();
    }


    @Override
    protected List<String> getCacheKeyItem(ExecutorContext context) {
        Long njId = context.getLive().getNjId();
        if (njId == null) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(String.valueOf(njId));
    }
}
