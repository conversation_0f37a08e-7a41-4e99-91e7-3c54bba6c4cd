package fm.lizhi.ocean.wave.permission.core.extension.role.filter.operation;

import fm.lizhi.ocean.wave.common.extension.ProcessorFactory;
import fm.lizhi.ocean.wave.permission.core.extension.role.IVotePermissionProcessor;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 投票权限过滤器
 * <AUTHOR>
 */
@Slf4j
@Component("operation_vote")
public class VoteOperationPermissionFilter extends AbstractOperationPermissionFilter {

    @Autowired
    private ProcessorFactory processorFactory;

    @Override
    public boolean businessEnvSupport(BusinessEvnEnum app) {
        return BusinessEvnEnum.HEI_YE.equals(app) || BusinessEvnEnum.PP.equals(app);
    }

    @Override
    public boolean hasPermission(PermissionFilterDto filterDto) {
        IVotePermissionProcessor votePermissionProcessor = processorFactory.getProcessor(filterDto.getAppId(), IVotePermissionProcessor.class);
        return votePermissionProcessor.hasPermission(filterDto);
    }
}
