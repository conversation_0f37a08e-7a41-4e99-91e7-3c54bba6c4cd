package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor;

/**
 * <AUTHOR>
 * @date 2025/2/27 17:30
 */
public class BeanExecutorValueFacade implements IBeanExecutorValue{

    private BeanExecutorValue beanExecutorValue;

    public BeanExecutorValueFacade(BeanExecutorValue beanExecutorValue) {
        this.beanExecutorValue = beanExecutorValue;
    }

    @Override
    public String getName() {
        return this.beanExecutorValue.getName();
    }

    @Override
    public int getCacheSecond() {
        return this.beanExecutorValue.getCacheSecond();
    }
}
