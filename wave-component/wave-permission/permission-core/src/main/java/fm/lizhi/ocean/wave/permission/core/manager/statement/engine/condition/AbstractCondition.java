package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition;

import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.InnerExecutorObject;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;

/**
 * 条件对象头像类
 * 泛型为value的类型
 * <AUTHOR>
 * @date 2025/2/14 18:28
 */
public abstract class AbstractCondition<T> implements InnerExecutorObject {

    protected String attribute;

    protected OperateSymbol operateSymbol;

    protected T value;

    protected AbstractCondition(ConditionDTO statement) {
        this.attribute = statement.getAttribute();
        this.operateSymbol = OperateSymbol.getByValue(statement.getOperateSymbol());
    }

    protected String genAttributeJsonPath(){
        return "$." + this.attribute;
    }

}
