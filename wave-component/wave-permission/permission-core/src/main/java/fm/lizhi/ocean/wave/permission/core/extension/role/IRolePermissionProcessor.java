package fm.lizhi.ocean.wave.permission.core.extension.role;

import fm.lizhi.ocean.wave.api.permission.param.PermissionCheckParam;
import fm.lizhi.ocean.wave.common.extension.IProcessor;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.permission.core.model.vo.RolePermissionVO;

/**
 * 角色权限个性化处理
 */
public interface IRolePermissionProcessor extends IProcessor<PermissionCheckParam, Void, RolePermissionVO, Void> {
    @Override
    default Class<? extends IProcessor> getBaseBusinessProcessor() {
        return IRolePermissionProcessor.class;
    }

    RolePermissionVO filter(PermissionFilterDto filterParam, RolePermissionVO result);

}
