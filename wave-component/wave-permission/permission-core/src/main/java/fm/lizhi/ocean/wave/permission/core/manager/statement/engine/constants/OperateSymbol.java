package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants;

import lombok.Getter;

/**
 * 运算符号
 * <AUTHOR>
 * @date 2025/2/14 16:09
 */
public enum OperateSymbol {

    /**
     * in 集合
     */
    IN("IN")
    , NOT_IN("NOT_IN")
    /**
     * 在指定用户组中
     */
    , IN_USERGROUP("IN_USERGROUP")
    /**
     * 不在用户组中
     */
    , NOT_IN_USERGROUP("NOT_IN_USERGROUP")
    , GT("GT")
    , LT("LT")
    , GE("GE")
    , LE("LE")
    , NUMBER_EQ("NUMBER_EQ")
    , STRING_EQ("STRING_EQ")
    , BEAN_EXECUTOR("BEAN_EXECUTOR")
    ;

    @Getter
    private String value;

    OperateSymbol(String value) {
        this.value = value;
    }

    public static OperateSymbol getByValue(String value) {
        for (OperateSymbol operateSymbol : OperateSymbol.values()) {
            if (operateSymbol.getValue().equals(value)) {
                return operateSymbol;
            }
        }
        return null;
    }


}
