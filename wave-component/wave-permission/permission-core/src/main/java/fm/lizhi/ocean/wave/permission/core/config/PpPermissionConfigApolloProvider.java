package fm.lizhi.ocean.wave.permission.core.config;

import fm.lizhi.ocean.wave.common.auto.route.common.annotation.ScanBusinessProviderAPI;
import org.springframework.context.annotation.Configuration;

/**
 * @description: PP服务 阿波罗配置类
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2023/08/26 18:15
 */
@Configuration
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.live.room.pp.api.LiveRoomRoleService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.live.room.pp.api.LiveRoomService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.pp.family.api.FamilyService.class),
})
public class PpPermissionConfigApolloProvider {

}
