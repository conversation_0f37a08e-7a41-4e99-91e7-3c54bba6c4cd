package fm.lizhi.ocean.wave.permission.core.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.api.live.bean.LiveRoomUserRole;
import fm.lizhi.ocean.wave.permission.core.constants.PermissionCodeEnum;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class RoomRoleManager {

    @Autowired
    private LiveRoomRoleService liveRoomRoleService;

    @Autowired
    private FamilyService familyService;

    /**
     * 判断用户是否是房间内的任一角色，如：
     * 房主、管理员、主持、超管、家族长、陪玩
     *
     * @param userId 用户ID
     * @param roomId 房间ID
     * @param njId   主播ID
     * @return 结果
     */
    public Result<Void> haveInRoomRole(long userId, long roomId, long njId) {
        //判断是否是厅主
        if (userId == njId) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        //再判断是否是房间内的其他角色
        Result<List<LiveRoomUserRole>> result = liveRoomRoleService.getLiveRoomUserAllRole(roomId, userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS && result.rCode() != LiveRoomRoleService.GET_LIVE_ROOM_USER_ALL_ROLE_NOT_FOUND) {
            return new Result<>(PermissionCodeEnum.IN_ROOM_ROLE_ROLE_ERROR.getCode(), null);
        }
        List<LiveRoomUserRole> liveRoomRoleList = result.target();
        //有数据说明是当前房间的角色
        if (CollectionUtils.isNotEmpty(liveRoomRoleList)) {
            log.info("RoomRoleManager.haveRoomRole has room role, userId:{}, roomId:{}, njId:{}", userId, roomId, njId);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        //判断是否是该厅的陪玩
        Result<Long> playerSignRes = familyService.playerCurSignNj(userId);
        if (playerSignRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return new Result<>(PermissionCodeEnum.IN_ROOM_ROLE_ROLE_ERROR.getCode(), null);
        }
        if (playerSignRes.target() == njId) {
            log.info("RoomRoleManager.haveRoomRole has player role, userId:{}, roomId:{}, njId:{}", userId, roomId, njId);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        //判断是否是该厅的家族长
        Result<List<Long>> familyNjRes = familyService.getFamilySignNjIds(userId, 0L);
        if (familyNjRes.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return new Result<>(PermissionCodeEnum.IN_ROOM_ROLE_ROLE_ERROR.getCode(), null);
        }

        List<Long> njIds = familyNjRes.target();
        if (CollectionUtils.isNotEmpty(njIds) && njIds.contains(njId)) {
            log.info("RoomRoleManager.haveRoomRole has family role, userId:{}, roomId:{}, njId:{}", userId, roomId, njId);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }
        return new Result<>(PermissionCodeEnum.IN_ROOM_ROLE_NO_ROLE_ERROR.getCode(), null);
    }

}
