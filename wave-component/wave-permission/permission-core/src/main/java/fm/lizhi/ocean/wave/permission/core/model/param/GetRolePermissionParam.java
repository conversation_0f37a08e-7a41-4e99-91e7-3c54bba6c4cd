package fm.lizhi.ocean.wave.permission.core.model.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/6/6
 */
@Data
public class GetRolePermissionParam {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long njId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long liveRoomId;


}
