package fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser;

import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.ConditionParser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.BeanExecutorCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/17 15:35
 */
@Slf4j
@Component
public class BeanExecutorParser implements ConditionParser {

    private ApplicationContext applicationContext;

    @Autowired
    public BeanExecutorParser(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public OperateSymbol getOperateSymbol() {
        return OperateSymbol.BEAN_EXECUTOR;
    }

    @Override
    public AbstractCondition<?> parser(ConditionDTO statement) {
        return new BeanExecutorCondition(statement, beanName -> {
            try {
                return applicationContext.getBean(beanName, AbstractBeanCondition.class);
            } catch (Exception e) {
                log.warn("get beanCondition beanName={} error: ", beanName, e);
                return null;
            }
        });
    }
}
