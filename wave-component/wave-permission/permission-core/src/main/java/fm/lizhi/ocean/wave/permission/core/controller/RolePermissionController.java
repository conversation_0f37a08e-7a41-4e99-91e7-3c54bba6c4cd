package fm.lizhi.ocean.wave.permission.core.controller;

import fm.lizhi.ocean.wave.api.permission.param.PermissionCheckParam;
import fm.lizhi.ocean.wave.permission.core.manager.PlatformRoleManager;
import fm.lizhi.ocean.wave.permission.core.model.param.GetRolePermissionParam;
import fm.lizhi.ocean.wave.permission.core.model.vo.RolePermissionVO;
import fm.lizhi.ocean.wave.server.common.auth.annotation.VerifyUserToken;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 用户权限Controller类
 * @author: guoyibin
 * @create: 2023/05/22 20:34
 */
@Slf4j
@RestController
@RequestMapping("/role")
public class RolePermissionController {

    @Autowired
    private PlatformRoleManager platformRoleManager;

    /**
     * 获取直播间内角色权限
     *
     * @param param
     * @return
     */
    @VerifyUserToken
    @GetMapping("/permission")
    public ResultVO<RolePermissionVO> getRolePermission(GetRolePermissionParam param) {
        PermissionCheckParam permissionCheckParam = PermissionCheckParam.builder()
                .appId(ContextUtils.getContext().getHeader().getAppId())
                .userId(ContextUtils.getContext().getUserId())
                .njId(param.getNjId())
                .roomId(param.getLiveRoomId())
                .build();
        return platformRoleManager.getRolePermission(permissionCheckParam);
    }
}
