package fm.lizhi.ocean.wave.permission.core.remote.pp;

import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.permission.core.remote.IActivitySceneServiceRemote;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PpActivitySceneServiceRemote extends RemoteServiceInvokeFacade implements IActivitySceneServiceRemote {
    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.PP.equals(evnEnum);
    }

    @Override
    public boolean checkPermission(long liveId, long userId) {
        return false;
    }
}
