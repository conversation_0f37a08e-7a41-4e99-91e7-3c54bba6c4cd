package fm.lizhi.ocean.wave.permission.core.manager.statement.engine;

import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.ResourceType;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 20:00
 */
@Getter
@Builder
public class Resource {

    private ResourceType type;

    private List<String> value;

    public void merge(Resource resource) {
        if (resource == null) {
            return;
        }
        if (resource.type != this.type) {
            return;
        }
        for (String r : resource.value) {
            if (this.value.contains(r)) {
                continue;
            }
            this.value.add(r);
        }
    }

}
