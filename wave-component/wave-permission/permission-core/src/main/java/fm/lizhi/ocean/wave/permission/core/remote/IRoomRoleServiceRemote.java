package fm.lizhi.ocean.wave.permission.core.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.IBaseRemoteServiceInvoker;

/**
 * 业务角色远程服务
 */
public interface IRoomRoleServiceRemote extends IBaseRemoteServiceInvoker {

    /**
     * 获取房间角色远程接口
     * @param userId
     * @param roomId
     * @param roleId
     * @return
     * if rCode == HAVE_ROOM_ROLE_ERROR(1): 获取房间角色失败
     * if rCode == HAVE_ROOM_NO_ROLE_ERROR(2): 没有角色
     */
    Result<Void> haveRoomRole(long userId, long roomId, int roleId);

    /**
     * 获取主持位远程接口
     * @param userId
     * @param njId
     * @return
     * if rCode == HAVE_HOST_ROLE_ERROR(1): 获取主持位失败
     * if rCode == HAVE_HOST_NO_ROLE_ERROR(2): 不是主持角色
     */
    Result<Void> haveHostRole(long userId, long njId);

    /**
     * 获取嘉宾位远程接口
     * @param userId
     * @return
     * if rCode == HAVE_GUEST_ROLE_ERROR(1): 获取嘉宾位失败
     * if rCode == HAVE_GUEST_NO_ROLE_ERROR(2): 不是嘉宾角色
     */
    Result<Void> haveGuestRole(long userId, long njId);

    /**
     * 获取房主位远程接口
     * @param userId
     * @param njId
     * @return
     * if rCode == HAVE_OWNER_ROLE_ERROR(1): 获取房主位失败
     * if rCode == HAVE_OWNER_NO_ROLE_ERROR(2): 不是房主角色
     */
    Result<Void> haveOwnerRole(long userId, long njId);


    int HAVE_ROOM_ROLE_ERROR = 1; // 获取房间角色失败
    int HAVE_ROOM_NO_ROLE_ERROR = 2; // 没有角色

    int HAVE_HOST_ROLE_ERROR = 1; // 获取主持位失败
    int HAVE_HOST_NO_ROLE_ERROR = 2; // 不是主持角色

    int HAVE_GUEST_ROLE_ERROR = 1; // 获取嘉宾位失败
    int HAVE_GUEST_NO_ROLE_ERROR = 2; // 不是嘉宾角色

    int HAVE_OWNER_ROLE_ERROR = 1; // 获取房主位失败
    int HAVE_OWNER_NO_ROLE_ERROR = 2; // 不是房主角色
    
}
