package fm.lizhi.ocean.wave.permission.core.extension.role.filter.operation;

import fm.lizhi.ocean.wave.permission.core.constants.RolePermissionEnum;
import fm.lizhi.ocean.wave.permission.core.extension.role.PermissionFilter;

/**
 * 操作权限抽象类
 * <AUTHOR>
 */
public abstract class AbstractOperationPermissionFilter implements PermissionFilter {


    @Override
    public boolean supportPermissionType(RolePermissionEnum permissionType) {
        if (null == permissionType){
            return false;
        }
        return RolePermissionEnum.ROLE_OPERATION_PERMISSION_ENUM.equals(permissionType);
    }
}
