package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.number;

import com.alibaba.fastjson.JSONPath;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/2/26 15:18
 */
public abstract class AbstractNumberCondition extends AbstractCondition<BigDecimal> {

    protected AbstractNumberCondition(ConditionDTO statement) {
        super(statement);
        super.value = new BigDecimal(String.valueOf(statement.getValue()));
    }

    protected abstract boolean doExecute(BigDecimal attributeValue, ExecutorContext context);

    @Override
    public boolean innerExecute(ExecutorContext context, IStatement statement) {
        Object attrValueObj = JSONPath.eval(context, genAttributeJsonPath());
        if (attrValueObj == null) {
            return false;
        }
        String attrValueStr = String.valueOf(attrValueObj);
        if (!NumberUtils.isCreatable(attrValueStr)) {
            return false;
        }

        BigDecimal attrValue = new BigDecimal(attrValueStr);
        return doExecute(attrValue, context);
    }
}
