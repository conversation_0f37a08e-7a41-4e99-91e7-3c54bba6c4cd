package fm.lizhi.ocean.wave.permission.core.remote;

import fm.lizhi.ocean.wave.common.auto.route.common.facade.IBaseRemoteServiceInvoker;

/**
 * 活动现场
 */
public interface IActivitySceneServiceRemote extends IBaseRemoteServiceInvoker {
    /**
     * 检查用户是否有权限
     *
     * @param liveId
     * @param userId
     * @return true: 有权限, false: 无权限
     */
    boolean checkPermission(long liveId, long userId);
}
