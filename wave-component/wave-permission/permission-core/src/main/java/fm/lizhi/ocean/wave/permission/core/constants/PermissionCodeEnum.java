package fm.lizhi.ocean.wave.permission.core.constants;

import fm.lizhi.ocean.wave.server.common.constant.IRCodes;

/**
 * @description: 权限模块错误码枚举类
 * @author: guoyibin
 * @create: 2023/05/19 19:02
 */
public enum PermissionCodeEnum implements IRCodes {

    /**
     * 权限错误码
     */
    NOT_VALIDATE_ROLE(105100, "非有效角色"),
    HAVE_NO_PERMISSION(105101, "无权限信息"),

    HAVE_ROOM_ROLE_ERROR(105102, "获取房间角色失败"),
    HAVE_ROOM_NO_ROLE_ERROR(105103, "没有角色"),
    HAVE_HOST_ROLE_ERROR(105104, "获取主持位失败"),
    HAVE_HOST_NO_ROLE_ERROR(105105, "不是主持角色"),
    HAVE_GUEST_ROLE_ERROR(105106, "获取嘉宾位失败"),
    HAVE_GUEST_NO_ROLE_ERROR(105107, "不是嘉宾角色"),
    HAVE_OWNER_ROLE_ERROR(105108, "获取房主位失败"),
    HAVE_OWNER_NO_ROLE_ERROR(105109, "不是房主角色"),
    NO_IN_ROOM_CREATOR_ROLE_ERROR(105110, "获取不在房生产者角色失败"),
    NO_IN_ROOM_CREATOR_NO_ROLE_ERROR(105111, "不是不在房生产者角色"),
    IN_ROOM_ROLE_ROLE_ERROR(105112, "查询是否是在房角色失败"),
    IN_ROOM_ROLE_NO_ROLE_ERROR(105113, "不是任一在房角色"),

    ENGINE_PARSER_ERROR(105114, "权限解析错误"),
    PERMISSION_LOAD_ERROR(105115, "权限加载异常, 请刷新重试"),
    ;

    PermissionCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误信息
     */
    private final String message;

    public String getMessage() {
        return message;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return this.message;
    }

}
