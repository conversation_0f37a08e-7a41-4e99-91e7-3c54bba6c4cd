package fm.lizhi.ocean.wave.permission.core.remote.bean;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 活动工具bean
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WaveActivityToolBean {

    /**
     * 类型
     * <p>
     * 字段对应的是 ActivityToolsInfoBean#toolValue
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String toolDesc;

    /**
     * 工具类型, 1:玩法; 2: 工具
     */
    private Integer toolType;
}
