package fm.lizhi.ocean.wave.permission.core.config;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class HyPermissionConfig {
    /**
     * 平台-业务角色映射
     */
    private Map<Integer, Integer> roleMappings = new HashMap<>();

    /**
     * 投票权限用户组ID
     */
    private Long votePermissionUserGroupId;

    /**
     * 房管超级权限支持最小版本
     */
    private int superPermissionMinVersion = 104847;

    /**
     * 黑叶跨房PK最小版本号
     */
     private int livePkMinVersion = 105451;

}