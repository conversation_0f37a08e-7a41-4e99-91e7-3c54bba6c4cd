package fm.lizhi.ocean.wave.permission.core.manager.statement.factory;

import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;

/**
 * 条件解析器标准接口
 * <AUTHOR>
 * @date 2025/2/14 19:58
 */
public interface ConditionParser {

    /**
     * 条件运算符
     * @return
     */
    OperateSymbol getOperateSymbol();

    /**
     * 解析为条件对象
     * @param statement
     * @return
     */
    AbstractCondition<?> parser(ConditionDTO statement);

}
