package fm.lizhi.ocean.wave.permission.core.config;

import fm.lizhi.live.room.xm.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.common.auto.route.common.annotation.ScanBusinessProviderAPI;
import fm.lizhi.xm.family.api.FamilyService;
import org.springframework.context.annotation.Configuration;
import xm.fm.lizhi.live.amusement.pp.api.PPNewLiveAmusementService;
import xm.fm.lizhi.studio.api.LiveRoomService;

/**
 * @description: 西米服务 阿波罗配置类
 * @author: liu<PERSON>lin
 * @create: 2023/08/26 18:15
 */
@Configuration
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = LiveRoomRoleService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = FamilyService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = LiveRoomService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PPNewLiveAmusementService.class),
})
public class XmPermissionConfigApolloProvider {

}
