package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.number;

import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;

import java.math.BigDecimal;

/**
 * 小于等于
 * <AUTHOR>
 * @date 2025/2/26 15:16
 */
public class NumberLeCondition extends AbstractNumberCondition {

    public NumberLeCondition(ConditionDTO statement) {
        super(statement);
    }

    @Override
    protected boolean doExecute(BigDecimal attributeValue, ExecutorContext context) {
        return attributeValue.compareTo(super.value) <= 0;
    }

}
