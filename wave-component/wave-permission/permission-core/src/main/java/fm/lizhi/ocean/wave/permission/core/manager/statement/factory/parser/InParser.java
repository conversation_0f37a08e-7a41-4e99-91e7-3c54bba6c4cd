package fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser;

import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.ConditionParser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.InCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/26 16:32
 */
@Component
public class InParser implements ConditionParser {

    @Override
    public OperateSymbol getOperateSymbol() {
        return OperateSymbol.IN;
    }

    @Override
    public AbstractCondition<?> parser(ConditionDTO statement) {
        return new InCondition(statement);
    }
}
