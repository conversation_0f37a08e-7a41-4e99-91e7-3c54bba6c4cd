package fm.lizhi.ocean.wave.permission.core.manager.statement.engine;

import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.Effect;

import java.util.List;

/**
 * 策略门面
 * <AUTHOR>
 * @date 2025/2/17 17:52
 */
public class StatementFacade implements IStatement{

    protected final Statement statement;

    public StatementFacade(Statement statement) {
        this.statement = statement;
    }

    @Override
    public Long getId() {
        return this.statement.getId();
    }

    @Override
    public Effect getEffect() {
        return this.statement.getEffect();
    }

    @Override
    public List<Resource> getResources() {
        return this.statement.getResources();
    }

}
