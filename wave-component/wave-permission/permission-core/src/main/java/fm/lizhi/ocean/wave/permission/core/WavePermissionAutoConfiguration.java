package fm.lizhi.ocean.wave.permission.core;

import fm.lizhi.ocean.wave.common.annotation.ApolloNamespaceInclude;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.SpringFactoriesLoader;

/**
 * 创作者直播自动配置, 基于{@value SpringFactoriesLoader#FACTORIES_RESOURCE_LOCATION}文件自动注册生效.
 *
 * @see SpringFactoriesLoader
 */
@ApolloNamespaceInclude({"wave-permission-config"})
@Configuration
@EnableConfigurationProperties({PermissionConfig.class})
public class WavePermissionAutoConfiguration {

}
