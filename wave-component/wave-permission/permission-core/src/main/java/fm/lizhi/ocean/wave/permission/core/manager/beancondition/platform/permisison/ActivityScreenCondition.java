package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.permisison;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import fm.lizhi.ocean.wave.permission.core.remote.ActivityApplyServiceRemote;
import fm.lizhi.ocean.wave.permission.core.remote.bean.WaveActivityApplyToolBean;
import fm.lizhi.ocean.wave.permission.core.remote.bean.WaveActivityToolBean;
import fm.lizhi.ocean.wave.permission.core.remote.request.GetInTimeRangeActivityApplyRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 活动白屏工具权限
 * <AUTHOR>
 * @date 2025/4/17 10:31
 */
@Slf4j
@Component
public class ActivityScreenCondition extends AbstractBeanCondition {

    @Autowired
    private ActivityApplyServiceRemote activityApplyServiceRemote;
    @Autowired
    private PermissionConfig permissionConfig;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long njId = context.getLive().getNjId();
        if (njId == null) {
            log.info("njId is null");
            return false;
        }

        int activityScreenBeforeMinute = permissionConfig.getActivityScreenBeforeMinute();

        // 查询活动列表
        Result<List<WaveActivityApplyToolBean>> result = activityApplyServiceRemote.getInTimeRangeActivityApply(GetInTimeRangeActivityApplyRequest.builder()
                .appId(context.getRequest().getAppId())
                .njId(njId)
                .startTimeBeforeMinute(activityScreenBeforeMinute)
                .endTimeAfterMinute(activityScreenBeforeMinute)
                .build());
        if (RpcResult.isFail(result)) {
            log.info("getInTimeRangeActivityApply fail. njId={},activityScreenBeforeMinute={}", njId, activityScreenBeforeMinute);
            return false;
        }
        List<WaveActivityApplyToolBean> activityList = result.target();
        if (CollectionUtils.isEmpty(activityList)) {
            log.info("activityList isEmpty. njId={},activityScreenBeforeMinute={}", njId, activityScreenBeforeMinute);
            return false;
        }

        // 判断权限
        for (WaveActivityApplyToolBean activity : activityList) {
            List<WaveActivityToolBean> activityToolList = activity.getActivityToolList();
            if (CollectionUtils.isEmpty(activityToolList)) {
                continue;
            }
            for (WaveActivityToolBean activityTool : activityToolList) {
                if (permissionConfig.getScreenToolName().equals(activityTool.getName())) {
                    return true;
                }
            }
        }

        log.info("screen tool not found. njId={},activityScreenBeforeMinute={}", njId, activityScreenBeforeMinute);
        return false;
    }

}
