package fm.lizhi.ocean.wave.permission.core.config;

import fm.lizhi.ocean.wave.common.auto.route.common.annotation.ScanBusinessProviderAPI;
import org.springframework.context.annotation.Configuration;

/**
 * @description: Hy阿波罗配置类
 * @author: guoyibin
 * @create: 2023/05/26 18:15
 */
@Configuration
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.live.room.hy.api.LiveRoomRoleService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.live.amusement.hy.api.HyNewLiveAmusementService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.hy.family.api.FamilyService .class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.live.room.hy.api.LiveRoomService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.live.room.pp.api.LiveRoomService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.live.room.pp.api.LiveRoomRoleService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.hy.idl.activityScene.api.ActivitySceneIDLService.class),
})
public class HyPermissionConfigApolloProvider {

}
