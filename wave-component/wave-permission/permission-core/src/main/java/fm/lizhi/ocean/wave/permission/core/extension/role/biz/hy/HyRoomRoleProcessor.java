package fm.lizhi.ocean.wave.permission.core.extension.role.biz.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.api.permission.param.PermissionCheckParam;
import fm.lizhi.ocean.wave.permission.core.extension.role.IRoomRoleProcessor;
import fm.lizhi.ocean.wave.permission.core.manager.PlatformRoleManager;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 黑叶房间角色权限差异化处理器
 */
@Component
public class HyRoomRoleProcessor implements IRoomRoleProcessor {

    @Autowired
    private PlatformRoleManager platformRoleManager;
    @Autowired
    private LiveRoomRoleService liveRoomRoleService;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public boolean isPgcFamily(String familyType) {
        return "C_FAMILY".equals(familyType);
    }

    @Override
    public boolean superMangerPreCheck(ExecutorContext context) {
        //黑叶超级管理员的前提，必须是管理员
        PermissionCheckParam.PermissionCheckParamBuilder builder = PermissionCheckParam.builder()
                .roomId(context.getLive().getRoomId())
                .appId(ContextUtils.getBusinessEvnEnum().getAppId())
                .njId(context.getLive().getNjId())
                .userId(context.getUser().getUserId())
                .platformRoleId(PlatformRoleEnum.ROLE_MANAGER.getRoleId());
        return platformRoleManager.havePermission(builder.build());
    }

    @Override
    public boolean hasSuperManager(ExecutorContext context) {
        Long roomId = context.getLive().getRoomId();
        Long userId = context.getUser().getUserId();
        if (roomId == null) {
            return false;
        }

        Result<Boolean> superResult = liveRoomRoleService.hasSuperPermission(roomId, userId);
        return superResult != null && superResult.rCode() == 0 && superResult.target();
    }

    @Override
    public List<Integer> roleVoListFilter(List<Integer> roleIds) {
        List<Integer> result = new ArrayList<>();
        for (Integer roleId : roleIds) {
            if (PlatformRoleEnum.ROLE_HOST.getRoleId() == roleId) {
                // 主持替换为管理员
                result.add(PlatformRoleEnum.ROLE_MANAGER.getRoleId());
            } else {
                result.add(roleId);
            }
        }
        return result;
    }
}
