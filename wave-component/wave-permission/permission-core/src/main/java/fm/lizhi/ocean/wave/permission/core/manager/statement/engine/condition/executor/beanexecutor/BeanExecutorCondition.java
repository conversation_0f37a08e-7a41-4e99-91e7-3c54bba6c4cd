package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ClassUtils;

import java.util.function.Function;

/**
 * bean执行器条件
 * <AUTHOR>
 * @date 2025/2/14 19:46
 */
@Slf4j
public class BeanExecutorCondition extends AbstractCondition<BeanExecutorValue> {

    private final Function<String, AbstractBeanCondition> getBeanFunction;

    public BeanExecutorCondition(ConditionDTO statement, Function<String, AbstractBeanCondition> getBeanFunction) {
        super(statement);
        BeanExecutorValue.BeanExecutorValueBuilder builder = BeanExecutorValue.builder();

        // 解析右值
        String valueJson = JsonUtil.dumps(statement.getValue());
        JSONObject obj = JSON.parseObject(valueJson);

        // 首字母转为小写
        builder.name(obj.getString("name"));
        builder.cacheSecond(obj.getInteger("cacheSecond"));

        super.value = builder.build();
        this.getBeanFunction = getBeanFunction;
    }

    @Override
    public boolean innerExecute(ExecutorContext context, IStatement statement) {
        AbstractBeanCondition bean = getBeanFunction.apply(StringUtils.uncapitalize(super.value.getName()));

        if (bean == null) {
            log.debug("bean {} is null", super.value.getName());
            return false;
        }

        return bean.execute(context, statement, new BeanExecutorValueFacade(super.value));
    }

}
