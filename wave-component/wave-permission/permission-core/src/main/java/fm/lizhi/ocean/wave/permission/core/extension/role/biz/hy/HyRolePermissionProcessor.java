package fm.lizhi.ocean.wave.permission.core.extension.role.biz.hy;

import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.permission.core.constants.RolePermissionEnum;
import fm.lizhi.ocean.wave.permission.core.extension.role.IRolePermissionProcessor;
import fm.lizhi.ocean.wave.permission.core.extension.role.filter.PermissionFilterHandler;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.permission.core.model.vo.RolePermissionVO;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 黑叶角色权限差异化处理器
 * @author: guoyibin
 * @create: 2023/06/06 16:19
 */
@Slf4j
@Component
public class HyRolePermissionProcessor implements IRolePermissionProcessor {

    @Autowired
    private PermissionFilterHandler permissionFilterHandler;

    @Override
    public ResultVO<Void> postprocessor(RolePermissionVO rolePremissionResult) {
        //黑叶房管与主持人角色外显信息一致
        rolePremissionResult.getRoles().forEach(role -> {
            if(role.getRoleId() == PlatformRoleEnum.ROLE_HOST.getRoleId()){
                role.setRoleId(PlatformRoleEnum.ROLE_MANAGER.getRoleId());
                role.setRoleName(PlatformRoleEnum.ROLE_MANAGER.getRoleName());
            }
        });
        return ResultVO.success();
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.HY;
    }

    @Override
    public RolePermissionVO filter(PermissionFilterDto filterParam, RolePermissionVO result) {

        List<String> modulesList = result.getModules().stream().filter(permission -> {
            filterParam.setPermissionType(RolePermissionEnum.ROLE_PAGE_PERMISSION_ENUM);
            filterParam.setPermission(permission);

            return permissionFilterHandler.filter(BusinessEvnEnum.HEI_YE, filterParam);
        }).collect(Collectors.toList());

        List<String> operationList = result.getOperations().stream().filter(permission -> {
            filterParam.setPermissionType(RolePermissionEnum.ROLE_OPERATION_PERMISSION_ENUM);
            filterParam.setPermission(permission);

            return permissionFilterHandler.filter(BusinessEvnEnum.HEI_YE, filterParam);
        }).collect(Collectors.toList());

        result.setModules(modulesList);
        result.setOperations(operationList);
        return result;
    }
}
