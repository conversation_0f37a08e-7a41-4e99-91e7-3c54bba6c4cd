package fm.lizhi.ocean.wave.permission.core.extension.role.filter.page;

import fm.lizhi.ocean.wave.permission.core.constants.RolePermissionEnum;
import fm.lizhi.ocean.wave.permission.core.extension.role.PermissionFilter;

/**
 * 页面权限抽象类
 * <AUTHOR>
 */
public abstract class AbstractPagePermissionFilter implements PermissionFilter {

    @Override
    public boolean supportPermissionType(RolePermissionEnum permissionType) {
        if (null == permissionType){
            return false;
        }
        return RolePermissionEnum.ROLE_PAGE_PERMISSION_ENUM.equals(permissionType);
    }
}
