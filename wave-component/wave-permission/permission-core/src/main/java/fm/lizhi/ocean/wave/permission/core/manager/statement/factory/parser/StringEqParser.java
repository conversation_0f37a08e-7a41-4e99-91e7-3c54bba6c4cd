package fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser;

import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.ConditionParser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.StringEqCondition;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/17 15:23
 */
@Component
public class StringEqParser implements ConditionParser {

    @Override
    public OperateSymbol getOperateSymbol() {
        return OperateSymbol.STRING_EQ;
    }

    @Override
    public AbstractCondition<?> parser(ConditionDTO statement) {
        return new StringEqCondition(statement);
    }
}
