package fm.lizhi.ocean.wave.permission.core.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description: 角色权限信息VO
 * @author: guoyibin
 * @create: 2023/05/20 14:36
 */
@Data
@Accessors(chain = true)
public class RolePermissionVO {
    /**
     * 页面模块权限
     */
    private List<String> operations;

    /**
     * 操作权限
     */
    private List<String> modules;

    /**
     * 角色列表
     */
    private List<PermissionRoleVO> roles;
}


