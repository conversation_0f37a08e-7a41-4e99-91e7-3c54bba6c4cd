package fm.lizhi.ocean.wave.permission.core.manager.statement.factory.parser;

import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.NotInCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.ConditionParser;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/26 16:32
 */
@Component
public class NotInParser implements ConditionParser {

    @Override
    public OperateSymbol getOperateSymbol() {
        return OperateSymbol.NOT_IN;
    }

    @Override
    public AbstractCondition<?> parser(ConditionDTO statement) {
        return new NotInCondition(statement);
    }
}
