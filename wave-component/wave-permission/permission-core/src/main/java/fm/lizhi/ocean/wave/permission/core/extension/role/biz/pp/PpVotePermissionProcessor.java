package fm.lizhi.ocean.wave.permission.core.extension.role.biz.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.constants.LiveModeEnum;
import fm.lizhi.ocean.wave.api.live.param.GetLiveModeRequestParam;
import fm.lizhi.ocean.wave.api.live.result.GetLiveModeResult;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import fm.lizhi.ocean.wave.permission.core.extension.role.IVotePermissionProcessor;
import fm.lizhi.ocean.wave.permission.core.extension.role.filter.PermissionFilterHandler;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: PP投票权限差异化处理器
 * @author: liuzilin
 * @create: 2023/08/23 16:19
 */
@Slf4j
@Component
public class PpVotePermissionProcessor implements IVotePermissionProcessor {

    @Autowired
    private PermissionFilterHandler permissionFilterHandler;
    @Autowired
    private LiveService liveService;
    @Autowired
    private PermissionConfig permissionConfig;

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.PP;
    }

    @Override
    public boolean hasPermission(PermissionFilterDto filterParam) {
        // TODO: 2024/10/8 版本覆盖后删除版本判断的代码
        int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
        if (clientVersion < permissionConfig.getPp().getVoteMinVersion()) {
            // 旧版本
            return false;
        }

        Long liveId = filterParam.getLiveId();
        if (liveId == null || liveId <= 0) {
            return false;
        }
        Result<GetLiveModeResult> result = liveService.getLiveMode(GetLiveModeRequestParam.builder().liveId(liveId).build());
        if (RpcResult.isFail(result)) {
            return false;
        }
        int liveMode = result.target().getLiveModeEnum().getLiveMode();
        return liveMode == LiveModeEnum.NORMAL_AMUSEMENT.getLiveMode()
                || liveMode == LiveModeEnum.NORMAL_VOCAL_ROOM.getLiveMode();
    }
}
