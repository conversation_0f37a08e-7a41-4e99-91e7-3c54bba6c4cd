package fm.lizhi.ocean.wave.permission.core.extension.role.filter.operation;

import com.alibaba.fastjson.JSON;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 房管超级权限操作权限
 */
@Slf4j
@Component("operation_set_super_admin")
public class SuperPermissionFilter extends AbstractOperationPermissionFilter {
    @Autowired
    private PermissionConfig permissionConfig;

    @Override
    public boolean businessEnvSupport(BusinessEvnEnum app) {
        return BusinessEvnEnum.HEI_YE.equals(app);
    }

    @Override
    public boolean hasPermission(PermissionFilterDto filterDto) {
        try {
            // 房管超级权限版本过滤
            // TODO: 2024/12/24 版本覆盖后删除版本判断的代码
            int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
            int superPermissionMinVersion = permissionConfig.getHy().getSuperPermissionMinVersion();

            return superPermissionMinVersion <= 0 || clientVersion > superPermissionMinVersion;
        } catch (NumberFormatException e) {
            log.error("SuperPermissionFilter happen error: dto={}", JSON.toJSONString(filterDto), e);
            //报错了不显示这个权限
            return false;
        }
    }
}
