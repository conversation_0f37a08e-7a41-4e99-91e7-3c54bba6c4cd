package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.role;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.api.live.bean.LiveRoomUserRole;
import fm.lizhi.ocean.wave.api.permission.constants.PlatformRoleEnum;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 管理员条件
 * <AUTHOR>
 * @date 2025/2/27 15:22
 */
@Slf4j
@Component
public class ManagerCondition extends AbstractBeanCondition {

    @Autowired
    private LiveRoomRoleService liveRoomRoleService;

    /**
     * 角色状态：正常
     */
    private final int ROLE_STATUS_NORMAL = 0;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        //获取业务管理员角色ID
        int roleId = permissionConfig.getBusinessRoleId(PlatformRoleEnum.ROLE_MANAGER.getRoleId(), context.getRequest().getAppId());
        Long roomId = context.getLive().getRoomId();
        Long userId = context.getUser().getUserId();

        if (roomId == null || roleId <= 0) {
            log.warn("param not validate, userId:{}, roomId:{}, roleId:{}, roomId is invalid", userId, roomId, roleId);
            return false;
        }

        Result<LiveRoomUserRole> liveRoomUserRoleResult = liveRoomRoleService.getLiveRoomUserRole(roomId, userId, roleId);
        if (liveRoomUserRoleResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("ManagerCondition error, userId:{}, roomId:{}, roleId:{}, rCode:{}",
                    userId, roomId, roleId, liveRoomUserRoleResult.rCode());
            return false;
        }

        LiveRoomUserRole liveRoomUserRole = liveRoomUserRoleResult.target();
        boolean haveRoleInfo = liveRoomUserRoleResult.target() != null;
        log.info("ManagerCondition success, userId:{}, roomId:{}, roleId:{}, has live room role:{},role status:{}",
                userId, roomId, roleId, haveRoleInfo, haveRoleInfo ? liveRoomUserRole.getStatus() : -1);

        return haveRoleInfo && liveRoomUserRole.getStatus() == ROLE_STATUS_NORMAL;
    }

}
