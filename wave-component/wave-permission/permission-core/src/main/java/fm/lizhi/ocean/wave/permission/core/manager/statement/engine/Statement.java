package fm.lizhi.ocean.wave.permission.core.manager.statement.engine;

import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.Effect;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 19:59
 */
@Slf4j
@Builder
public class Statement implements IStatement {

    @Getter
    private Long id;

    @Getter
    private Effect effect;

    @Getter
    private List<Resource> resources;

    private ConditionGroup conditionGroup;

    public boolean execute(ExecutorContext context) {
        if (this.conditionGroup == null) {
            log.debug("conditionGroup is null");
            return false;
        }

        return this.conditionGroup.innerExecute(context, new StatementFacade(this));
    }
}
