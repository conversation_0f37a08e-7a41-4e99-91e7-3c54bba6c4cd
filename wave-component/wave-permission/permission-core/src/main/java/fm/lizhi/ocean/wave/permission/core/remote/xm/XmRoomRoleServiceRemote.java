package fm.lizhi.ocean.wave.permission.core.remote.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.xm.api.LiveRoomRoleService;
import fm.lizhi.live.room.xm.api.LiveRoomService;
import fm.lizhi.live.room.xm.protocol.LiveRoomProto;
import fm.lizhi.live.room.xm.protocol.LiveRoomRoleProto;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.permission.core.remote.IRoomRoleServiceRemote;
import fm.lizhi.xm.family.api.FamilyService;
import fm.lizhi.xm.family.protocol.FamilyServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.amusement.pp.api.PPNewLiveAmusementService;
import xm.fm.lizhi.live.amusement.pp.protocol.LiveAmusementProto;

import java.util.List;


/**
 * @description: 西米用户角色服务远程服务，角色服务目前分散在业务的不同dc接口里面，这个远程服务对所有业务的DC进行了功能的聚合（是否合适待定）
 */
@Slf4j
@Component
public class XmRoomRoleServiceRemote extends RemoteServiceInvokeFacade implements IRoomRoleServiceRemote {

    @Autowired
    private LiveRoomRoleService liveRoomRoleService;
    @Autowired
    private FamilyService familyService;
    @Autowired
    private LiveRoomService liveRoomService;
    @Autowired
    private PPNewLiveAmusementService newLiveAmusementService;

    /**
     * 角色状态：正常
     */
    private static final int ROLE_STATUS_NORMAL = 0;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.XIMI.equals(evnEnum);
    }

    @Override
    public Result<Void> haveRoomRole(long userId, long roomId, int roleId) {
        if (roomId <= 0) {
            log.warn("xm RoomRoleServiceRemote.haveRoomRole param not validate, userId:{}, roomId:{}, roleId:{}, roomId is invalid",
                    userId, roomId, roleId);
            return new Result<>(IRoomRoleServiceRemote.HAVE_ROOM_NO_ROLE_ERROR, null);
        }
        Result<LiveRoomRoleProto.ResponseGetLiveRoomUserRole> liveRoomUserRoleResult = liveRoomRoleService.getLiveRoomUserRole(roomId, userId, roleId);
        if (liveRoomUserRoleResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm RoomRoleServiceRemote.haveRoomRole error, userId:{}, roomId:{}, roleId:{}, rCode:{}",
                    userId, roomId, roleId, liveRoomUserRoleResult.rCode());
            return new Result<>(IRoomRoleServiceRemote.HAVE_ROOM_ROLE_ERROR, null);
        }

        boolean haveRoleInfo = liveRoomUserRoleResult.target().hasLiveRoomRole();

        log.info("xm RoomRoleServiceRemote.haveRoomRole success, userId:{}, roomId:{}, roleId:{}, has live room role:{},role status:{}",
                userId, roomId, roleId, haveRoleInfo, haveRoleInfo ? liveRoomUserRoleResult.target().getLiveRoomRole().getStatus() : -1);

        if (haveRoleInfo && liveRoomUserRoleResult.target().getLiveRoomRole().getStatus() == ROLE_STATUS_NORMAL) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        return new Result<>(IRoomRoleServiceRemote.HAVE_ROOM_NO_ROLE_ERROR, null);
    }

    @Override
    public Result<Void> haveHostRole(long userId, long njId) {
        Result<LiveAmusementProto.ResponseAllRoomHosts> allRoomHostsResult = newLiveAmusementService.getAllRoomHosts(njId);
        if (allRoomHostsResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm RoomRoleServiceRemote.haveHostRole error, userId:{}, njId:{}, rCode:{}", userId, njId, allRoomHostsResult.rCode());
            return new Result<>(IRoomRoleServiceRemote.HAVE_HOST_ROLE_ERROR, null);
        }

        // 这里直接做角色判断，否则需要多写一个bean进行承载和转换，没有必要
        List<Long> roomHostUidList = allRoomHostsResult.target().getLiveRoomHostUidList();
        boolean containUser = roomHostUidList.contains(userId);

        log.info("xm RoomRoleServiceRemote.haveHostRole invoke success, userId:{}, njId:{}, containUser:{}", userId, njId, containUser);

        return new Result<>(containUser
                ? GeneralRCode.GENERAL_RCODE_SUCCESS
                : IRoomRoleServiceRemote.HAVE_HOST_NO_ROLE_ERROR, null);
    }

    @Override
    public Result<Void> haveGuestRole(long userId, long njId) {
        Result<FamilyServiceProto.ResponseGetUserFamilyV2> userFamilyResult = familyService.getUserFamilyV2(userId, false);
        if (userFamilyResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm haveGuestRole get user family error, userId:{},rCode:{}", userId, userFamilyResult.rCode());
            return new Result<>(IRoomRoleServiceRemote.HAVE_GUEST_ROLE_ERROR, null);
        }
        FamilyServiceProto.UserFamilyInfoV2 userFamilyInfo = userFamilyResult.target().getUserFamilyInfo();
        log.info("xm haveGuestRole check family type or isPlayer, userId:{},familyType:{},isPlayer:{}",
                userId, userFamilyInfo.getFamilyType(), userFamilyInfo.getIsPlayer());
        //判断是否公司家族陪玩  个人家族:UGC 公司家族:PGC
        if ("PGC".equals(userFamilyInfo.getFamilyType()) && isFamilyOrPlayer(userFamilyInfo)) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }
        return new Result<>(IRoomRoleServiceRemote.HAVE_GUEST_NO_ROLE_ERROR, null);
    }

    @Override
    public Result<Void> haveOwnerRole(long userId, long njId) {
        //基于角色判断的特殊性，直接调用底层的live接口获取数据做房主判断，不再引用live模块的api包
        Result<LiveRoomProto.ResponseGetLiveRoomByUserId> getLiveRoomResult = liveRoomService.getLiveRoomByUserId(userId);
        if (getLiveRoomResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm RoomRoleServiceRemote.haveOwnerRole error, userId:{}, njId:{}, rCode:{}", userId, njId, getLiveRoomResult.rCode());
            return new Result<>(IRoomRoleServiceRemote.HAVE_OWNER_ROLE_ERROR, null);
        }

        log.info("xm RoomRoleServiceRemote.haveOwnerRole success, userId:{}, njId:{}", userId, njId);

        if (getLiveRoomResult.target().getLiveRoom().getUserId() == njId) {
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        return new Result<>(IRoomRoleServiceRemote.HAVE_OWNER_NO_ROLE_ERROR, null);
    }

    private static boolean isFamilyOrPlayer(FamilyServiceProto.UserFamilyInfoV2 userFamilyInfo) {
        return userFamilyInfo.getIsPlayer() || userFamilyInfo.getIsFamily();
    }
}
