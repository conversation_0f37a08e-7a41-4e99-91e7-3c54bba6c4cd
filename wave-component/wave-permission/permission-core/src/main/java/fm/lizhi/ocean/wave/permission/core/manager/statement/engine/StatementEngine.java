package fm.lizhi.ocean.wave.permission.core.manager.statement.engine;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.common.util.CountDownLatchWrapper;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.permission.core.config.PermissionConfig;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.Effect;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;

/**
 * 策略规则引擎，包含缓存等逻辑实现
 * <AUTHOR>
 * @date 2025/2/17 17:55
 */
@Slf4j
@Component
public class StatementEngine {

    @Autowired
    private PermissionConfig permissionConfig;

    /**
     * 线程池
     * 拒绝策略使用当前线程执行
     */
    private final ExecutorService executorService = ThreadUtils.getExecutors(
            "statementEngine.execute", 20, 30, new ArrayBlockingQueue<Runnable>(500)
            , ThreadUtils.getCallerRunsPolicyExecutionHandler());

    /**
     * 执行策略
     * @param statementFacade
     * @param context
     * @return
     */
    public StatementResult execute(StatementFacade statementFacade, ExecutorContext context){
        StatementResult.StatementResultBuilder builder = StatementResult.builder();

        Statement statement = statementFacade.statement;
        boolean result = statement.execute(context);
        if (result) {
            if (statement.getEffect() == Effect.ALLOW) {
                builder.allowResources(statement.getResources());
            }
            if (statement.getEffect() == Effect.DENY) {
                builder.denyResources(statement.getResources());
            }
        }

        return builder.build();
    }

    /**
     * 执行批量策略
     * @param statementFacades
     * @param context
     * @return
     */
    public StatementResult execute(List<StatementFacade> statementFacades, ExecutorContext context){
        if (CollectionUtils.isEmpty(statementFacades)) {
            return StatementResult.builder().build();
        }

        List<Resource> allowResources = new CopyOnWriteArrayList<>();
        List<Resource> denyResources = new CopyOnWriteArrayList<>();

        // 分批执行
        List<List<StatementFacade>> partition = Lists.partition(statementFacades, permissionConfig.getPermissionEngineThreadTaskNum());
        CountDownLatchWrapper wrapper = new CountDownLatchWrapper(executorService, 5, partition.size());
        for (List<StatementFacade> facades : partition) {
            wrapper.submit(() -> {
                for (StatementFacade statementFacade : facades) {
                    boolean result = false;
                    Statement statement = statementFacade.statement;
                    try {
                        result = statement.execute(context);
                        log.debug("statement execute id={},result={}", statement.getId(), result);
                    } catch (Exception e) {
                        log.error("statementEngine execute error:", e);
                    }

                    if (result) {
                        if (statement.getEffect() == Effect.ALLOW) {
                            allowResources.addAll(statement.getResources());
                        }
                        if (statement.getEffect() == Effect.DENY) {
                            denyResources.addAll(statement.getResources());
                        }
                    }
                }
            });
        }
        wrapper.await();

        return StatementResult.builder()
                .allowResources(allowResources)
                .denyResources(denyResources)
                .build();
    }

}
