package fm.lizhi.ocean.wave.permission.core.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 角色权限表
 *
 * @date 2023-05-22 06:11:22
 */
@Table(name = "`wave_role_permission`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WaveRolePermission {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 角色ID
     */
    @Column(name= "`role_id`")
    private Integer roleId;

    /**
     * 权限ID
     */
    @Column(name= "`permission_id`")
    private Long permissionId;

    /**
     * 状态 0 无效 1 有效
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 权限创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 权限修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 业务id
     */
    @Column(name= "`app_id`")
    private Integer appId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", roleId=").append(roleId);
        sb.append(", permissionId=").append(permissionId);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", appId=").append(appId);
        sb.append("]");
        return sb.toString();
    }
}