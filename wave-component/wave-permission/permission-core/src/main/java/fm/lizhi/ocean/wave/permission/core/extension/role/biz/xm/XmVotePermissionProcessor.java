package fm.lizhi.ocean.wave.permission.core.extension.role.biz.xm;

import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.permission.core.extension.role.IVotePermissionProcessor;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description: XM投票权限差异化处理器
 * @author: l<PERSON><PERSON><PERSON>
 * @create: 2023/08/23 16:19
 */
@Slf4j
@Component
public class XmVotePermissionProcessor implements IVotePermissionProcessor {

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.XM;
    }

    @Override
    public boolean hasPermission(PermissionFilterDto filterParam) {
        return false;
    }
}
