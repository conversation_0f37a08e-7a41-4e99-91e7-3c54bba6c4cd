package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.role;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.extension.role.IRoomRoleProcessor;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import fm.lizhi.ocean.wave.user.bean.FamilyInfoBean;
import fm.lizhi.ocean.wave.user.result.UserFamilyInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 陪玩条件
 * <AUTHOR>
 * @date 2025/2/27 15:46
 */
@Slf4j
@Component
public class GuestCondition extends AbstractBeanCondition {

    @Autowired
    private FamilyService familyService;
    @Autowired
    private ProcessorV2Factory factory;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long userId = context.getUser().getUserId();
        Long njId = context.getLive().getNjId();
        if (njId == null) {
            return false;
        }

        Result<UserFamilyInfoResult> userFamilyResult = familyService.getUserFamily(userId, false);
        if (userFamilyResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("GuestCondition get user family error, userId:{},rCode:{}", userId, userFamilyResult.rCode());
            return false;
        }

        UserFamilyInfoResult userFamilyInfo = userFamilyResult.target();
        log.info("GuestCondition check family type or isPlayer, familyType:{},isPlayer:{}",
                userFamilyInfo.getFamilyType(), userFamilyInfo.isPlayer());

        IRoomRoleProcessor processor = factory.getProcessor(IRoomRoleProcessor.class);
        //如果是陪玩，还要判断是否是当前的主播的陪玩
        if (userFamilyInfo.isPlayer() && Objects.equals(userFamilyInfo.getNjId(), njId)) {
            return true;
        }

        //如果不是PGC家族或者不是家族长，就不用判断是否是该家族的签约厅
        if (!processor.isPgcFamily(userFamilyInfo.getFamilyType()) || !userFamilyInfo.isFamily()) {
            return false;
        }

        //查询当前厅主的家族信息
        Result<FamilyInfoBean> familyRes = familyService.getUserInFamily(njId);
        if (RpcResult.isFail(familyRes)) {
            return false;
        }

        //当前用户和厅主的签约家族的家族长是同一个人，则有陪玩权限
        return Objects.equals(familyRes.target().getUserId(), userId);
    }

}
