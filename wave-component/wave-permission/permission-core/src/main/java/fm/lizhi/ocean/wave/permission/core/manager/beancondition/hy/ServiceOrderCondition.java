package fm.lizhi.ocean.wave.permission.core.manager.beancondition.hy;

import api.activity.api.ServiceOrderService;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 服务单权限条件
 */
@Slf4j
@Component
public class ServiceOrderCondition extends AbstractBeanCondition {

    @Autowired
    private ServiceOrderService serviceOrderService;

    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {
        Long userId = context.getUser().getUserId();
        Long liveId = context.getLive().getLiveId();
        if (userId == null || liveId == null) {
            return false;
        }

        Result<Boolean> result = serviceOrderService.checkUserHasServiceOrderPermission(userId, liveId);
        if (RpcResult.isFail(result)){
            // 默认不放行
            return false;
        }
        return result.target();
    }
}