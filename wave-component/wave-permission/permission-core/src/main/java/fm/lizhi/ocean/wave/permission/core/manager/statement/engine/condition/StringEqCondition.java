package fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition;

import com.alibaba.fastjson.JSONPath;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;

/**
 * <AUTHOR>
 * @date 2025/2/14 18:26
 */
public class StringEqCondition extends AbstractCondition<String> {

    public StringEqCondition(ConditionDTO statement) {
        super(statement);
        super.value = String.valueOf(statement.getValue());
    }

    @Override
    public boolean innerExecute(ExecutorContext context, IStatement statement) {
        Object attrValueStr = JSONPath.eval(context, genAttributeJsonPath());
        if (attrValueStr == null) {
            return false;
        }
        return super.value.equals(String.valueOf(attrValueStr));
    }

}
