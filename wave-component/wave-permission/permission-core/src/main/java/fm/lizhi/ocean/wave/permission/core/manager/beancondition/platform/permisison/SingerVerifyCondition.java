package fm.lizhi.ocean.wave.permission.core.manager.beancondition.platform.permisison;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.manager.statement.ExecutorContext;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.IStatement;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.executor.beanexecutor.AbstractBeanCondition;
import fm.lizhi.ocean.wave.user.api.FamilyService;
import fm.lizhi.ocean.wave.user.api.SingerService;
import fm.lizhi.ocean.wave.user.param.CheckSingerPermissionParam;
import fm.lizhi.ocean.wave.user.result.UserFamilyInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 歌手认证
 * <AUTHOR>
 */
@Slf4j
@Component
public class SingerVerifyCondition extends AbstractBeanCondition {

    @Autowired
    private FamilyService familyService;

    @Autowired
    private SingerService singerService;


    @Override
    protected boolean doExecute(ExecutorContext context, IStatement statement) {

        // 校验家族长和厅主, 如果不放行，直接返回
        if (!checkFamilyPermission(context)){
            return false;
        }

        Result<Boolean> result = singerService.checkSingerPermissionCache(new CheckSingerPermissionParam()
                .setAppId(context.getRequest().getAppId())
                .setNjId(context.getLive().getNjId())
                .setLiveId(context.getLive().getLiveId())
                .setUserId(context.getUser().getUserId())
        );

        if (RpcResult.isFail(result)){
            log.warn("SingerVerifyCondition.checkSingerPermission fail, userId:{}", context.getUser().getUserId());
            return false;
        }

        return result.target();
    }

    @Override
    protected List<String> getCacheKeyItem(ExecutorContext context) {
        Long userId = context.getUser().getUserId();
        Long njId = context.getLive().getNjId();
        if (njId == null || userId == null) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(String.valueOf(userId), String.valueOf(njId));
    }

    /**
     * 校验厅主和家族长
     */
    private boolean checkFamilyPermission(ExecutorContext context) {
        Long userId = context.getUser().getUserId();
        Long njId = context.getLive().getNjId();
        if (njId == null) {
            return false;
        }

        // 获取签约厅厅主, 不展示
        Result<Long> signNjId = familyService.playerCurSignNj(userId);
        if (RpcResult.isFail(signNjId)) {
            log.info("SingerVerifyCondition.getUserInFamily fail, userId:{}", userId);
            return false;
        }
        if (signNjId.target().equals(userId)){
            log.info("SingerVerifyCondition.checkFamilyPermission user is nj, userId:{}", userId);
            return false;
        }

        // 获取家族长, 不展示
        Result<UserFamilyInfoResult> familyMaster = familyService.getUserFamilyByCache(userId, true);
        if (RpcResult.isFail(familyMaster)) {
            log.info("SingerVerifyCondition.checkFamilyPermission familyMaster fail, userId:{}", userId);
            return false;
        }
        if (familyMaster.target().isFamily()) {
            log.info("SingerVerifyCondition.checkFamilyPermission user is familyMaster, userId:{}", userId);
            return false;
        }
        return true;

    }

}
