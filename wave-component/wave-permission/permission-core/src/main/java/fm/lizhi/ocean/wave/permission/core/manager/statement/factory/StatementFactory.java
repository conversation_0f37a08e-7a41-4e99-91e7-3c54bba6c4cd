package fm.lizhi.ocean.wave.permission.core.manager.statement.factory;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.wave.common.util.WaveAssert;
import fm.lizhi.ocean.wave.permission.core.constants.PermissionCodeEnum;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.*;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionDTO;
import fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto.ConditionGroupDTO;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.condition.AbstractCondition;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.Effect;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.LogicSymbol;
import fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.ResourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 策略工厂
 * <AUTHOR>
 * @date 2025/2/14 18:12
 */
@Slf4j
@Component
public class StatementFactory {

    private Map<String, ConditionParser> conditionParserMap = new HashMap<>();

    @Autowired
    public StatementFactory(List<ConditionParser> conditionParsers) {
        for (ConditionParser conditionParser : conditionParsers) {
            conditionParserMap.put(conditionParser.getOperateSymbol().getValue(), conditionParser);
        }
    }

    /**
     * 根据策略生成策略对象
     * @param statementDsls
     * @return
     */
    public List<StatementFacade> createStatement(List<String> statementDsls){
        return statementDsls.stream().map(this::createStatement).collect(Collectors.toList());
    }

    /**
     * 根据策略的dsl生成策略对象
     * @param statementDsl
     * @return
     */
    public StatementFacade createStatement(String statementDsl) {
        JSONObject dslJson = JSONObject.parseObject(statementDsl);
        Long id = dslJson.getLong("id");
        String effect = dslJson.getString("effect");

        // 解析资源
        JSONArray resourcesArr = dslJson.getJSONArray("resources");
        List<Resource> resources = new ArrayList<>();
        if (resourcesArr != null) {
            for (int i = 0; i < resourcesArr.size(); i++) {
                JSONObject resourceObj = resourcesArr.getJSONObject(i);
                // 资源类型
                String type = resourceObj.getString("type");
                // 资源值
                JSONArray valueArr = resourceObj.getJSONArray("value");
                List<String> valueList = valueArr.stream().map(String::valueOf).collect(Collectors.toList());

                resources.add(Resource.builder()
                        .type(ResourceType.getByValue(type))
                        .value(valueList)
                        .build());
            }
        }

        // 解析条件组
        ConditionGroupDTO conditionGroup = dslJson.getObject("conditionGroup", ConditionGroupDTO.class);
        Statement statement = Statement.builder()
                .id(id)
                .effect(Effect.getByValue(effect))
                .resources(resources)
                .conditionGroup(parserConditionGroup(conditionGroup))
                .build();
        return new StatementFacade(statement);
    }

    /**
     * 解析条件组
     * @param groupDTO
     * @return
     */
    private ConditionGroup parserConditionGroup(ConditionGroupDTO groupDTO){
        if (groupDTO == null) {
            return null;
        }

        ConditionGroup.ConditionGroupBuilder builder = ConditionGroup.builder();
        builder.logicSymbol(LogicSymbol.getByValue(groupDTO.getLogicSymbol()));

        List<AbstractCondition<?>> conditionResult = new ArrayList<>();
        List<ConditionDTO> conditionList = groupDTO.getConditionList();
        for (ConditionDTO conditionDTO : conditionList) {
            conditionResult.add(parserCondition(conditionDTO));
        }
        builder.conditionList(conditionResult);

        // 递归解析下一层条件组
        ConditionGroupDTO nextGroupDTO = groupDTO.getConditionGroup();
        if (nextGroupDTO != null && CollectionUtils.isNotEmpty(nextGroupDTO.getConditionList())) {
            builder.conditionGroup(parserConditionGroup(nextGroupDTO));
        }
        return builder.build();
    }

    /**
     * 解析条件
     * @param conditionDTO
     * @return
     */
    private AbstractCondition<?> parserCondition(ConditionDTO conditionDTO) {
        ConditionParser conditionParser = conditionParserMap.get(conditionDTO.getOperateSymbol());
        WaveAssert.notNull(conditionParser, PermissionCodeEnum.ENGINE_PARSER_ERROR);
        return conditionParser.parser(conditionDTO);
    }

}
