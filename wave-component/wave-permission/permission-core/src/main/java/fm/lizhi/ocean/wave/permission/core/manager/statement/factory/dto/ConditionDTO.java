package fm.lizhi.ocean.wave.permission.core.manager.statement.factory.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/2/14 17:32
 */
@Data
@Accessors(chain = true)
public class ConditionDTO {

    /**
     * 条件左值
     */
    private String attribute;

    /**
     * 运算符
     * @see fm.lizhi.ocean.wave.permission.core.manager.statement.engine.constants.OperateSymbol
     */
    private String operateSymbol;

    /**
     * 条件右值
     */
    private Object value;

}
