package fm.lizhi.ocean.wave.permission.core.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.OffsetDateTime;


/**
 * @description: 配置类，先简单写，后续按照规范改造
 * @author: guoyibin
 * @create: 2023/05/20 10:10
 */
@Data
@ConfigurationProperties(prefix = "wave-permission")
public class PermissionConfig {

    /**
     * 麦序福利表v2新包版本号. 注意配置时必须和AssistantConfig保持一致
     */
    private int checkInV2MinimumVersion = 105209;

    /**
     * 麦序福利表v1版本禁用时间, 配置示例: 2025-01-17T00:00:00+08:00. 注意配置时必须和AssistantConfig保持一致
     */
    private OffsetDateTime checkInV1DisabledTime = OffsetDateTime.parse("2025-01-17T19:00:00+08:00");

    /**
     * 麦序福利PGC家族权限判断开关
     */
    private Boolean enableCheckInFamilyPgcPermission = true;

    /**
     * 权限引擎单线程执行任务数
     */
    private int permissionEngineThreadTaskNum = 5;

    /**
     * 权限引擎缓存最大过期时间，单位秒
     */
    private int permissionEngineExecutorMaxCacheSeconds = 60 * 15;

    /**
     * 活动投屏功能提前开放分钟数
     */
    private int activityScreenBeforeMinute = 15;

    /**
     * 投屏工具名称
     */
    private String screenToolName = "投屏工具";


    private HyPermissionConfig hy = new HyPermissionConfig();

    private PpPermissionConfig pp = new PpPermissionConfig();

    private XmPermissionConfig xm = new XmPermissionConfig();

    /**
     * 根据平台角色id获取业务角色id
     * @param platformRoleId
     * @param appId
     * @return
     */
    public Integer getBusinessRoleId(int platformRoleId, int appId) {
        if (BusinessEvnEnum.HEI_YE.appId() == appId) {
            return hy.getRoleMappings().get(platformRoleId);
        }
        if (BusinessEvnEnum.PP.appId() == appId) {
            return pp.getRoleMappings().get(platformRoleId);
        }
        if (BusinessEvnEnum.XIMI.appId() == appId) {
            return xm.getRoleMappings().get(platformRoleId);
        }
        return -1;
    }

}




