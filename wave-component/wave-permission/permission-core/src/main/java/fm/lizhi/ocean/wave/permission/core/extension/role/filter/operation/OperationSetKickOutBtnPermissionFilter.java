package fm.lizhi.ocean.wave.permission.core.extension.role.filter.operation;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.permission.core.model.dto.PermissionFilterDto;
import fm.lizhi.ocean.wave.platform.api.live.param.CheckKickOutPermissionNotConfigReq;
import fm.lizhi.ocean.wave.platform.api.live.service.LiveKickOutService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 踢出房间过滤器
 * <AUTHOR>
 */
@Slf4j
@Component("operation_set_kick_out_btn")
public class OperationSetKickOutBtnPermissionFilter extends AbstractOperationPermissionFilter {

    @Autowired
    private LiveKickOutService liveKickOutService;


    @Override
    public boolean businessEnvSupport(BusinessEvnEnum app) {
        return BusinessEvnEnum.PP.equals(app) || BusinessEvnEnum.XIMI.equals(app) || BusinessEvnEnum.HEI_YE.equals(app);
    }


    @Override
    public boolean hasPermission(PermissionFilterDto filterDto) {

        CheckKickOutPermissionNotConfigReq permissionReq = CheckKickOutPermissionNotConfigReq.builder()
                .liveRoomId(filterDto.getLiveRoomId())
                .njId(filterDto.getNjId())
                .operateUserId(filterDto.getUserId())
                .build();

        // 校验差异化权限
        Result<Boolean> result = liveKickOutService.checkKickOutPermissionNotConfig(permissionReq);
        if (RpcResult.isFail(result)){
            // 踢人权限，默认不放行
            return false;
        }
        return result.target();
    }
}
