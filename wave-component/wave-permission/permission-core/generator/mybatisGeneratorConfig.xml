<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="mysql" targetRuntime="MyBatis3Simple">
        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <!-- datastore插件 -->
        <!--
            targetProject: 生成文件的输出目录。
            targetPackage: 生成Mapper接口的包名。
            mapperSuffix: 生成Mapper接口名后缀，比如指定Mapper，实体类名为Order，则生成的Mapper类名为OrderMapper。
            namespace: 数据库配置的namespace，会自动增加到生成的Mapper接口上。
            enableLombok: 是否使用Lombok，默认false
                true：会在生成的实体类增加lombok相关的@Setter、@Getter、@ToString和@EqualsAndHashCode等注解；
                false：生成setter和getter方法。
        -->
        <plugin type="fm.lizhi.common.datastore.mysql.mybatis.generator.DataStoreMybatisPlugin">
            <property name="targetProject" value="src/main/java"/>
            <property name="targetPackage" value="fm.lizhi.ocean.wave.permission.core.datastore.mapper"/>
            <property name="mapperSuffix" value="Mapper"/>
            <property name="namespace" value="mysql_ocean_wave"/>
            <property name="enableLombok" value="true"/>
            <property name="enableExampleClass" value="true"/>
        </plugin>

        <!-- 数据库实体类注释生成器 -->
        <commentGenerator type="fm.lizhi.common.datastore.mysql.mybatis.generator.DataStoreCommentGenerator">
        </commentGenerator>

        <!-- 数据库配置 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*******************************************************************************************"
                        userId="root"
                        password="db_admin#ops.fm">
            <property name="useInformationSchema" value="true"/>
        </jdbcConnection>

        <javaTypeResolver type="fm.lizhi.common.datastore.mysql.mybatis.generator.DataStoreJavaTypeResolver">
            <!--
                true：DECIMAL/NUMERIC => BigDecimal
                false：default,
                    scale>0 length>18     => BigDecimal
                    scale=0 length[10,18] => Long
                    scale=0 length <= 9   => Integer
             -->
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 实体类生成器配置 -->
        <javaModelGenerator targetPackage="fm.lizhi.ocean.wave.permission.core.datastore.entity" targetProject="src/main/java">
            <property name="constructorBased" value="false"/>
            <property name="enableSubPackages" value="true"/>
            <property name="immutable" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- 数据表配置 -->
        <!--
            schema: 数据库名
            tableName: 数据库中的表名
            domainObjectName: 生成的实体类名
        -->
        <table schema="ocean_wave" tableName="wave_role" domainObjectName="WaveRole">
            <property name="useActualColumnNames" value="false"/>
            <!--
                用于自动生成主键字段标识，需要自动生成（荔枝ID生成器）主键值时增加该属性:
                    column: 数据库中的主键字段名
            -->
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <table schema="ocean_wave" tableName="wave_permission" domainObjectName="WavePermission">
            <property name="useActualColumnNames" value="false"/>
            <!--
                用于自动生成主键字段标识，需要自动生成（荔枝ID生成器）主键值时增加该属性:
                    column: 数据库中的主键字段名
            -->
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

        <table schema="ocean_wave" tableName="wave_role_permission" domainObjectName="WaveRolePermission">
            <property name="useActualColumnNames" value="false"/>
            <!--
                用于自动生成主键字段标识，需要自动生成（荔枝ID生成器）主键值时增加该属性:
                    column: 数据库中的主键字段名
            -->
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

    </context>
</generatorConfiguration>
