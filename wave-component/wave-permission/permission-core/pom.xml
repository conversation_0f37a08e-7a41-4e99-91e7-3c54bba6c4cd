<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wave</groupId>
        <artifactId>wave-permission</artifactId>
        <version>1.5.26</version>
    </parent>

    <artifactId>permission-core</artifactId>

    <dependencies>
        <!-- ====================        创作者内部依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>permission-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>live-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>user-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>activity-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>lz-ocean-wave-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-api</artifactId>
        </dependency>


        <!-- ====================        基础架构的依赖        ==================== -->

        <!-- 大部分由wave-common引入, 不足部分在这里补全 -->


        <!-- ====================        黑叶服务依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-room-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-hy-amusement-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.hy.family</groupId>
            <artifactId>lz-hy-family-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-activity-api</artifactId>
        </dependency>

        <!-- ====================        PP服务依赖        ==================== -->

        <dependency>
            <groupId>fm.pp.family</groupId>
            <artifactId>lz-pp-family-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.live</groupId>
            <artifactId>lz-pp-room-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-pp-api</artifactId>
        </dependency>

        <!-- ====================        西米服务依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-room-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-xm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-family-api</artifactId>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
