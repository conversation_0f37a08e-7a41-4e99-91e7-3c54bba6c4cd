package fm.lizhi.ocean.wave.live.core.dao.redis;

import fm.lizhi.ocean.wave.common.datastore.redis.CacheKeyGenerator;

/**
 * 支付收入redis key
 *
 * <AUTHOR>
 */
public enum PayIncomeRedisKey implements CacheKeyGenerator.CacheKeyType {

    /**
     * 厅收入缓存
     * key: WAVE_LIVE_ROOM_INCOME_HASH_#{roomId}_#{day}
     * field: 收入类型code
     * value: 收入金额
     * expire: 1天
     */
    ROOM_INCOME_HASH,

    ;

    @Override
    public String getPrefix() {
        return "WAVE_LIVE";
    }

    @Override
    public String getKey(Object... args) {
        StringBuilder sb = new StringBuilder(this.getPrefix());

        switch (this) {
            default:
                sb.append("_");
                sb.append(this.name());
                break;
        }

        for (Object o : args) {
            sb.append("_" + o);
        }
        return sb.toString();
    }
}
