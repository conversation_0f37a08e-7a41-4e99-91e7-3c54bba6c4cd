package fm.lizhi.ocean.wave.live.core.config;

import java.util.List;

public interface CommonLiveConfig {

    int getComeSource();

    int getLiveTitleMinLength();

    int getLiveTitleMaxLength();

    int getLiveAnnouncementMaxLength();

    String getLiveRoomBlockText();

    String getRoomManagerIconUrl();

    String getRoomOwnerIconUrl();

    float getUserRoleImageBadgeAspect();

    /**
     * 搜索白名单（userId），逗号分隔
     */
    List<Long> getSearchWhitelist();

    /**
     * 是否禁用搜索
     */
    boolean isDisableSearch();

    /**
     * 禁用搜索文案
     */
    String getDisableSearchMsg();

    /**
     * 抽取搜索内容中的数字
     */
    boolean isSearchExtractDigit();

    /**
     * 最大的搜索内容长度
     */
    int getSearchMaxLen();

    /**
     * 最小的搜索内容长度
     */
    int getSearchMinLen();

    /**
     * 超过最大搜索长度的文案
     */
    String getExceedMaxLenMsg();

    /**
     * 最小搜索长度的文案
     */
    String getExceedMinLenMsg();

    /**
     * 获取缺省欢迎语
     *
     * @return
     */
    String getDefaultLiveWelcomeMsg();

    String getWealthImgUrlIndex();

    /**
     * 直播间分享链接
     *
     * @return 返回链接地址
     */
    String getLiveShareUrl();

    /**
     * 最小弹窗优化-显示品类下的直播数量
     *
     * @return 返回数量, 默认20
     */
    int getMinDialogOptLive();

    /**
     * 官方频道列表,存主播ID
     */
    List<Long> getOfficialRoomNjIdList();

    /**
     * 是否启用容灾接口
     */
    boolean isHomeLiveDisaster();

    /**
     * 厅收入配置
     *
     * @return 收入配置
     */
    String getRoomIncomeConfig();

    /**
     * 超管图片
     * 暂时仅黑叶有设置该图片 icon ,其他端超管图片暂时是空串
     */
    String getRoomSuperManagerIconUrl();
}
