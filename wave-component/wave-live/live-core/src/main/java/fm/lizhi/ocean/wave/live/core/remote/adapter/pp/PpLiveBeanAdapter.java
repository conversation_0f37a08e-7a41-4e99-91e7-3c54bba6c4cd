package fm.lizhi.ocean.wave.live.core.remote.adapter.pp;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.live.room.pp.protocol.LiveNewProto;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.bean.pp.PpLiveExtraBean;

import java.util.Date;

/**
 * @description: 平台与业务live转换
 * @author: guoyibin
 * @create: 2023/08/23 20:11
 */
public class PpLiveBeanAdapter {

    public static LiveBean convert(LiveNewProto.Live live, String cdnHost) {
        return LiveBean.builder()
                .id(live.getId())
                .liveRoomId(live.getLiveRoomId())
                .name(live.getName())
                .userId(live.getUserId())
                .introduction(live.getIntroduction())
                .imageUrl(UrlUtils.addCdnHost(cdnHost, live.getImageUrl()))
                .startTime(new Date(live.getStartTime()))
                .endTime(new Date(live.getEndTime()))
                .actualStartTime(new Date(live.getActualStartTime()))
                .actualEndTime(live.getActualEndTime() > 0 ? new Date(live.getActualEndTime()) : null)
                .createTime(new Date(live.getCreateTime()))
                .modifyTime(new Date(live.getModifyTime()))
                .status(live.getStatus())
                .shareUrl(live.getShareUrl())
                .extraJson(JsonUtil.dumps(PpLiveExtraBean.builder()
                        .tags(live.getTags()).mark(live.getMark()).type(live.getType()).build()))
                .build();
    }
}
