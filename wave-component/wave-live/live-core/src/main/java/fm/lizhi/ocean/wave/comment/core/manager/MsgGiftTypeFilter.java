package fm.lizhi.ocean.wave.comment.core.manager;

import fm.lizhi.ocean.wave.comment.core.remote.bean.TransientComment;
import hy.fm.lizhi.datacenter.comment.pp.constant.CommentTypeExtension;

import java.util.ArrayList;
import java.util.List;


/**
 * 送礼消息过滤
 * <AUTHOR>
 */
public class MsgGiftTypeFilter implements CommentFilter {

    @Override
    public List<TransientComment> filter(List<TransientComment> comments) {
        List<TransientComment> result = new ArrayList<>();
        for (TransientComment comment : comments) {
            if (CommentTypeExtension.GIFT == comment.getCommentTypeExtension()) {
                result.add(comment);
            }
        }

        return result;
    }
}
