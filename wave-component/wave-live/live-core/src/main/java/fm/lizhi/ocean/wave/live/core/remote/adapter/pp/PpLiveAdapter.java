package fm.lizhi.ocean.wave.live.core.remote.adapter.pp;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.wave.live.core.remote.bean.PerformanceDataBean;
import fm.lizhi.ocean.wave.live.core.remote.result.HomeRecResponse;
import fm.lizhi.pprec.platform.protocol.RecPlatformProto;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class PpLiveAdapter {

    /**
     * 转换首页推荐直播间返回结果
     *
     * @param recPlatform 推荐平台返回结果
     * @param freshType   刷新类型
     * @return 结果
     */
    public HomeRecResponse convertHomeRecResponse(RecPlatformProto.ResponseRecPlatform recPlatform, int freshType) {
        List<RecPlatformProto.RcmdItem> itemList = recPlatform.getItemList();
        List<Long> liveIds = itemList.stream().map(RecPlatformProto.RcmdItem::getItemId).collect(Collectors.toList());
        String extraResult = recPlatform.getExtraJson();
        JSONObject extraResultJson = JSONObject.parseObject(extraResult);
        boolean lastPage = false;
        if (extraResultJson.containsKey("isLastPage")) {
            lastPage = extraResultJson.getBoolean("isLastPage");
        }
        Integer newDataVersionTime = extraResultJson.getInteger("newDataVersionTime");
        PerformanceDataBean performanceData = new PerformanceDataBean()
                .setPageNo(extraResultJson.getIntValue("pageNo"))
                .setTimestamp(newDataVersionTime)
                .setFreshType(freshType);
        return HomeRecResponse.builder()
                .liveIdList(liveIds)
                .lastPage(lastPage)
                .performanceData(performanceData)
                .build();
    }
}
