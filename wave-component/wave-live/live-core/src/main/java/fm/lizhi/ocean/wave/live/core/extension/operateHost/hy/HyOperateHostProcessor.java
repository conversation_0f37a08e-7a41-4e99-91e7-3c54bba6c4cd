package fm.lizhi.ocean.wave.live.core.extension.operateHost.hy;

import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.live.core.extension.operateHost.OperateHostProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HyOperateHostProcessor implements OperateHostProcessor {

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.HY;
    }

}
