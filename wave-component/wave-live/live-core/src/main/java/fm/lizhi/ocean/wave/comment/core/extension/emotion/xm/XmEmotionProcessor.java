package fm.lizhi.ocean.wave.comment.core.extension.emotion.xm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentContants;
import fm.lizhi.ocean.wave.comment.core.constants.LiveConstants;
import fm.lizhi.ocean.wave.comment.core.extension.emotion.IEmotionProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.emotion.bean.EmotionPostBean;
import fm.lizhi.ocean.wave.comment.core.model.mapper.EmotionMapper;
import fm.lizhi.ocean.wave.comment.core.model.result.EmotionResult;
import fm.lizhi.ocean.wave.comment.core.model.vo.EmotionVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.GroupEmotionVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EmotionInfo;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetEmotionByTypeRequest;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetEmotionsByTypeResponse;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetTotalEmotionsResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.ILiveEmotionServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.constant.FunctionConstant;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.util.ClientVersionUtil;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.xm.vip.api.oss.LiveEmotionService;
import fm.lizhi.xm.vip.protocol.oss.LiveEmotionProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/8 19:52
 */
@Slf4j
@Component
public class XmEmotionProcessor implements IEmotionProcessor {


    @Resource
    private CommentConfig commentConfig;
    @Autowired
    private LiveEmotionService liveEmotionService;
    @Autowired
    private CommonProviderConfig commonProviderConfig;
    @MyAutowired
    private ILiveEmotionServiceRemote iLiveEmotionServiceRemote;

    @Override
    public ResultVO<EmotionResult> postprocessor(EmotionPostBean data) {
        GetTotalEmotionsResult totalEmotionsResult = data.getGetTotalEmotionsResult();
        BusinessConfig businessConfig = data.getBusinessConfig();
        EmotionResult emotionResult = new EmotionResult();

        // 根据EmotionIndex倒序
        Comparator<EmotionInfo> byIndexOrdering = Comparator.comparingLong(EmotionInfo::getEmotionIndex).reversed();
        // 根据修改时间倒序，最近修改的在前面
        Comparator<EmotionInfo> byModifyTimeOrdering = Comparator.comparingLong(EmotionInfo::getModifyTime).reversed();

        // 合并排序条件
        Comparator<EmotionInfo> ordering = byIndexOrdering.thenComparing(byModifyTimeOrdering);

        // 排序
        totalEmotionsResult.getEmotionInfoList().sort(ordering);
        // 处理表情包列表
        Map<Long, List<EmotionVO>> groupMapList = new HashMap<>();
        for (EmotionInfo emotionInfo : totalEmotionsResult.getEmotionInfoList()) {
            long groupId = emotionInfo.getGroupId() == null ? 1L : emotionInfo.getGroupId();
            List<EmotionVO> emotionList = groupMapList.computeIfAbsent(groupId, k -> new ArrayList<>());

            EmotionVO emotionVO = new EmotionVO();
            emotionVO.setEmotionId(String.valueOf(emotionInfo.getEmotionId()));
            emotionVO.setImage(UrlUtils.addCdnHost(businessConfig.getCdnHost(), emotionInfo.getImageUrl()));
            emotionVO.setName(emotionInfo.getName());
            emotionVO.setAspect(emotionInfo.getAspect());
            emotionVO.setFactor(emotionInfo.getFactor());
            emotionVO.setRepeatCount(emotionInfo.getRepeatCount());
            emotionVO.setRepeatStopImages(emotionInfo.getResultUrls().stream()
                    .map(url -> UrlUtils.addCdnHost(businessConfig.getCdnHost(), url)).collect(Collectors.toList()));
            emotionVO.setSvgaUrl(UrlUtils.addCdnHost(businessConfig.getCdnHost(), emotionInfo.getSvgaUrl()));
            emotionVO.setPcAniUrl(UrlUtils.addCdnHost(businessConfig.getCdnHost(), emotionInfo.getPcAniUrl()));
            emotionVO.setType(CommentContants.EmotionType.DEFAULT);
            emotionVO.setReview(Boolean.TRUE);
            emotionList.add(emotionVO);
        }

        // 查询表情包分组icon
        List<GroupEmotionVO> groupList = new ArrayList<>();
        for (Map.Entry<Long, List<EmotionVO>> entry : groupMapList.entrySet()) {
            Long groupId = entry.getKey();
            if (groupId == 0 || entry.getValue().isEmpty()) {
                continue;
            }
            GroupEmotionVO groupEmotionVO = new GroupEmotionVO();
            groupEmotionVO.setGroupId(String.valueOf(groupId));
            groupEmotionVO.setEmotions(entry.getValue());
            groupList.add(groupEmotionVO);
        }

        // 将互动表情加入到表情包列表中
        if (Objects.equals(data.getLoadDiyEmotion(), LiveConstants.LoadDiyEmotion.YES)
                && Objects.equals(data.getType(), LiveConstants.EmotionSource.LIVE)) {
            List<EmotionVO> diyEmo = listInteractiveEmotion();
            if (CollectionUtils.isNotEmpty(diyEmo)) {
                groupList.add(new GroupEmotionVO()
                        .setGroupIcon(commentConfig.getXm().getActionEmotionGroupIconUrl())
                        .setEmotions(diyEmo)
                        .setGroupId(CommentContants.diyEmotionGroupId));
            }
        }

        // 添加贵族表情包
        String version = commonProviderConfig.getFunctionVersion().get(FunctionConstant.XM_VIP_EMOTION);
        if (ClientVersionUtil.isGeVersion(version)) {
            Result<GetEmotionsByTypeResponse> emotionsByTypeResult = iLiveEmotionServiceRemote.getEmotionsByType(GetEmotionByTypeRequest.builder()
                    .type(1)
                    .build());
            if (RpcResult.isSuccess(emotionsByTypeResult)) {
                List<EmotionVO> emotionVOS = EmotionMapper.I.infos2vos(emotionsByTypeResult.target().getEmotions());
                for (EmotionVO emotionVO : emotionVOS) {
                    emotionVO.setSvgaUrl(UrlUtils.addCdnHost(businessConfig.getCdnHost(), emotionVO.getSvgaUrl()));
                    emotionVO.setPcAniUrl(UrlUtils.addCdnHost(businessConfig.getCdnHost(), emotionVO.getPcAniUrl()));
                }
                groupList.add(new GroupEmotionVO()
                        .setGroupIcon(commentConfig.getXm().getActionEmotionGroupIconUrl())
                        .setEmotions(emotionVOS)
                        .setDisplay(false) // 付费功能，无需展示
                        .setGroupId(CommentContants.vipEmotionGroupId));
            }
        }

        // 关系表情
        Result<GetEmotionsByTypeResponse> relEmotionResult = iLiveEmotionServiceRemote.getEmotionsByType(GetEmotionByTypeRequest.builder()
                .type(2)
                .build());
        if (RpcResult.isSuccess(relEmotionResult)) {
            List<EmotionVO> emotionVOS = EmotionMapper.I.infos2vos(relEmotionResult.target().getEmotions());
            for (EmotionVO emotionVO : emotionVOS) {
                emotionVO.setSvgaUrl(UrlUtils.addCdnHost(businessConfig.getCdnHost(), emotionVO.getSvgaUrl()));
                emotionVO.setPcAniUrl(UrlUtils.addCdnHost(businessConfig.getCdnHost(), emotionVO.getPcAniUrl()));
            }
            groupList.add(new GroupEmotionVO()
                    .setGroupIcon(commentConfig.getXm().getActionEmotionGroupIconUrl())
                    .setEmotions(emotionVOS)
                    .setDisplay(false) // 付费功能，无需展示
                    .setGroupId(CommentContants.relationEmotionGroupId));
        }

        emotionResult.setGroupEmotionList(groupList);
        emotionResult.setEmotionList(groupList.stream().flatMap(x->x.getEmotions().stream()).collect(Collectors.toList()));
        emotionResult.setVersion(totalEmotionsResult.getVersion());
        emotionResult.setIsRefresh(true);

        return ResultVO.success(emotionResult);
    }


    /**
     * 获取互动表情列表
     *
     * @return 互动表情列表
     */
    private List<EmotionVO> listInteractiveEmotion() {
        Result<LiveEmotionProto.ResponseGetEmotionList> emotionListResult = liveEmotionService.getEmotionList();
        if (emotionListResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm.listInteractiveEmotion,fail,rCode={}", emotionListResult.rCode());
            return Collections.emptyList();
        }

        List<LiveEmotionProto.EmotionInfo> emotionsList = emotionListResult.target().getEmotionsList();
        if (CollectionUtils.isEmpty(emotionsList)) {
            return Collections.emptyList();
        }


        String actionEmotionImageJson = commentConfig.getXm().getActionEmotionImageJson();
        JSONObject imageUrlObj = new JSONObject();
        if (StringUtils.isNotBlank(actionEmotionImageJson)) {
            imageUrlObj = JSON.parseObject(actionEmotionImageJson);
        }

        List<EmotionVO> actionEmotions = new ArrayList<>();
        for (LiveEmotionProto.EmotionInfo emotionInfo : emotionsList) {
            EmotionVO ae = new EmotionVO();
            ae.setEmotionId(String.valueOf(emotionInfo.getEmotionId()));
            ae.setImage(emotionInfo.getImage());
            ae.setName(emotionInfo.getName());
            ae.setAspect(emotionInfo.getAspect());
            ae.setFactor(emotionInfo.getFactor());
            ae.setRepeatCount(emotionInfo.getRepeatCount());
            ae.setReview(Boolean.TRUE);
            ae.setType(CommentContants.EmotionType.INTERACTIVE);
            ae.setRepeatStopImages(emotionInfo.getEmotionImagesList()
                    .stream()
                    .map(LiveEmotionProto.EmotionImages::getImageUrl)
                    .collect(Collectors.toList())
            );
            ae.setGifImageUrl(emotionInfo.getGifImageUrl());

            if (imageUrlObj.containsKey(ae.getEmotionId())) {
                ae.setImage(imageUrlObj.getString(ae.getEmotionId()));
            }

            actionEmotions.add(ae);
        }

        return actionEmotions;
    }

    @Override
    public List<EmotionVO> getInteractiveEmotion(int emotionSourceType) {
        if (LiveConstants.EmotionSource.LIVE == emotionSourceType) {
            List<EmotionVO> emotionVOS = listInteractiveEmotion();
            if (CollectionUtils.isEmpty(emotionVOS)) {
                return new ArrayList<>();
            }
            return emotionVOS;
        }
        return new ArrayList<>();
    }

    @Override
    public CommentCommonConfig getConfig() {
        return commentConfig.getXm();
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.XM;
    }
}
