package fm.lizhi.ocean.wave.live.core.model.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetLiveUserParam {

    @NotNull(message = "参数错误")
    @JsonSerialize(using = ToStringSerializer.class)
    private long userId;

    @NotNull(message = "参数错误")
    @JsonSerialize(using = ToStringSerializer.class)
    private long liveId;
}
