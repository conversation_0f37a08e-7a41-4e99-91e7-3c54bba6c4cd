package fm.lizhi.ocean.wave.comment.core.model.bean.xm;

import fm.lizhi.ocean.wave.comment.core.model.bean.RoomRouletteWheelCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.vo.RoomRouletteWheelCommentVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 房间转盘信息
 *
 * <AUTHOR>
 */
@Data
public class XmRoomRouletteWheelCommentExtra implements RoomRouletteWheelCommentExtra {

    /**
     * 消息卡片标题
     */
    private String title;


    /**
     * 选项列表
     */
    private List<RoomRouletteWheelCommentVO.RoomRouletteWheelOptionVO> options;

    private Integer resultOptionId;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RoomRouletteWheelOptionVO {
        /**
         * 选项id
         */
        private Integer optionId;

        /**
         * 选项名称
         */
        private String name;

        /**
         * 权重
         */
        private Integer weight;
    }

}
