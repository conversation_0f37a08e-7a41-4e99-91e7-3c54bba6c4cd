package fm.lizhi.ocean.wave.comment.core.manager;

import com.dianping.cat.Cat;
import com.dianping.cat.builder.Metric;
import com.dianping.cat.builder.MetricBuilder;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentCatMetricName;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 评论业务监控
 *
 * <AUTHOR>
 * @date 2023/07/30
 */
@Slf4j
@Component
public class CommentMonitorManager {

    @Autowired
    private CommentConfig commentConfig;

    /**
     * 评论查询统计
     *
     * @param type      类型， 1：评论，2：进房消息
     * @param fromCache 是否从缓存
     */
    public void commentQueryState(int type, boolean fromCache) {
        try {
            if (!commentConfig.isCommentMonitorSwitch()) {
                return;
            }
            Metric metric = MetricBuilder.getInstance().addMetric(CommentCatMetricName.COMMENT_QUERY_STATE.getName());
            metric.setDataPointCount();
            metric.addTag("appName", BusinessEvnEnum.from(ContextUtils.getContext().getHeader().getAppId()).getName());
            metric.addTag("fromCache", String.valueOf(fromCache ? 1 : 0));
            metric.addTag("type", String.valueOf(type));
            Cat.logMetric(metric);
        } catch (Exception e) {
            log.warn("commentQueryState fail:", e);
        }
    }

}
