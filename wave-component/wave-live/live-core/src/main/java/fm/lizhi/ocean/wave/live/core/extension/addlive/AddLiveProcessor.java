package fm.lizhi.ocean.wave.live.core.extension.addlive;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.extension.IProcessor;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.addlive.bean.AddLivePostBean;
import fm.lizhi.ocean.wave.live.core.extension.addlive.bean.AddLivePreBean;
import fm.lizhi.ocean.wave.live.core.model.param.AddLiveParam;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
public interface AddLiveProcessor extends IProcessor<AddLivePreBean, Void, AddLivePostBean, Void> {

    @Override
    default Class<? extends IProcessor> getBaseBusinessProcessor() {
        return AddLiveProcessor.class;
    }


    CommonLiveConfig getLiveCommonConfig();

    default String liveNamePreProcessor(AddLiveParam param, Long liveRoomId) {
        return param.getLiveName();
    }

    void checkParam(AddLiveParam param);

    /**
     * room type 特殊处理
     * @param preBean
     */
    default void setRoomType(AddLivePreBean preBean) {
    }
}
