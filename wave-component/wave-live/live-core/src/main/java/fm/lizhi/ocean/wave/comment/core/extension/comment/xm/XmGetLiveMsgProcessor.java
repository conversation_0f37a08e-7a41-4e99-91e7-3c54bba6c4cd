package fm.lizhi.ocean.wave.comment.core.extension.comment.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.extension.comment.IGetLiveMsgProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.*;
import fm.lizhi.ocean.wave.comment.core.manager.EnterNoticeExtra;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.mapper.CommentMapper;
import fm.lizhi.ocean.wave.comment.core.model.vo.LiveCommentVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.SongInfoVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.UserVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.ContainsUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.common.util.ModelMapperUtils;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.GrowRelationLevelService;
import fm.lizhi.ocean.wave.user.api.MedalService;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.api.UserWealthLevelService;
import fm.lizhi.ocean.wave.user.result.FreshUserResult;
import fm.lizhi.ocean.wave.user.result.MedalResult;
import fm.lizhi.ocean.wave.user.result.ShowMealRelationResult;
import fm.lizhi.ocean.wave.user.result.WealthLevelResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.datacenter.comment.pp.constant.CommentTypeExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Description: 获取评论差异化处理类
 */
@Slf4j
@Component
public class XmGetLiveMsgProcessor implements IGetLiveMsgProcessor {

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private WealthMedalNode wealthMedalNode;

    @Autowired
    private UserMedalListNode userMedalListNode;

    @Autowired
    private FreshUserMedalNode freshUserMedalNode;

    @Autowired
    private UserWealthLevelService userWealthLevelService;

    @Autowired
    private MedalService medalService;

    @Autowired
    private GrowRelationLevelService growRelationLevelService;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private UserRoleMedalNode userRoleMedalNode;

    @Autowired
    private UserService userService;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public CommentCommonConfig getCommentConfig() {
        return commentConfig.getXm();
    }


    @Override
    public void adaptUserVo(TransientComment transientComment, UserVO userVO) {

        fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleUser simpleUser = transientComment.getSimpleUser();
        if (simpleUser.getUserId() == Long.parseLong(userVO.getUserId())) {
            userVO.setNameColorsList(simpleUser.getNameColorsList()); // 昵称颜色
            userVO.setRoomVipUrls(simpleUser.getRoomVipUrls()); // 用户的勋章
            userVO.setUserRoomVipStatus(simpleUser.getUserRoomVipStatus());
        }
    }

    @Override
    public List<ICommentMedalNode> getBuildCommentMedalNodes() {
        List<ICommentMedalNode> nodes = new ArrayList<>(8);
        //不同的业务需要的勋章节点不同，且顺序不同
        //- 角色勋章
        //- 财富等级信息
        //- 新用户勋章
        //- 勋章列表
        nodes.add(userRoleMedalNode);
        nodes.add(wealthMedalNode);
        nodes.add(freshUserMedalNode);
        nodes.add(userMedalListNode);
        return nodes;
    }

    @Override
    public List<ICommentMedalNode> getBuildEnterNoticeMedalNodes() {
        List<ICommentMedalNode> nodes = new ArrayList<>(8);
        //2. 设置财富等级勋章
        //3. 设置新用户勋章
        nodes.add(wealthMedalNode);
        nodes.add(freshUserMedalNode);
        return nodes;
    }

    @Override
    public boolean isCanGetEnterNotice(long liveId) {
        return commentConfig.getXm().isEnterNoticeOn()
                && !ContainsUtils.contains(commentConfig.getXm().getBigStarLiveLiveIds(), liveId);
    }

    @Override
    public ResultVO<TransientComment> resetCommentUserProperty(TransientComment transientComment) {
        //- 财富等级信息
        //- 新用户勋章
        //- 勋章列表
        //只针对系统公告评论做用户属性数据重置
        boolean needReset = transientComment.getCommentType() == WaveCommentType.SYSTEM_NOTICE_COMMENT && transientComment.getOperatorUserId() > 0;
        if (!needReset) {
            return ResultVO.success(transientComment);
        }

        //拷贝数据
        TransientComment targetComment = ModelMapperUtils.MODEL_MAPPER.map(transientComment, TransientComment.class);
        //查询勋章列表信息
        Result<List<MedalResult>> medalListRes = medalService.getMedalListFromCache(targetComment.getUserId());

        List<MedalResult> medalList = medalListRes.target();
        if (!CollectionUtils.isEmpty(medalList)) {
            List<Medal> medals = new ArrayList<>(medalList.size());
            for (MedalResult medalResult : medalList) {
                Medal medal = new Medal();
                medal.setCover(medalResult.getCover());
                medal.setAspect(medalResult.getAspect());
                medals.add(medal);
            }
            targetComment.setMedal(medals);
        } else {
            //失败就不显示
            targetComment.setMedal(new ArrayList<>());
        }

        //查询财富等级信息
        Result<WealthLevelResult> wealthRes = userWealthLevelService.getWealthLevelFromCache(targetComment.getUserId());

        WealthLevelResult wealth = wealthRes.target();
        if (wealth != null) {
            WealthLevelDTO wealthLevel = new WealthLevelDTO();
            wealthLevel.setCover(wealth.getCover());
            wealthLevel.setLevel(wealth.getLevel());
            wealthLevel.setAspect(wealth.getAspect());
            wealthLevel.setCover(buildWealthUrl(wealthLevel));
            targetComment.setWealthLevel(wealthLevel);
        } else {
            //失败就不显示
            targetComment.setWealthLevel(null);
        }

        //是否是新用户查询
        Result<FreshUserResult> freshUserResult = userService.isFreshUserByCache(targetComment.getUserId());
        if (freshUserResult.rCode() == 0 && freshUserResult.target() != null) {
            FreshUserResult userResult = freshUserResult.target();
            FreshUser freshUser = FreshUser.builder().is(userResult.isIs()).url(userResult.getUrl()).aspect(userResult.getAspect()).build();
            targetComment.setFreshUser(freshUser);
        } else {
            //失败就不显示
            targetComment.setFreshUser(null);
        }
        return ResultVO.success(targetComment);
    }

    @Override
    public boolean isEnterComment(TransientComment transientComment) {
        return transientComment.getCommentTypeExtension() == CommentTypeExtension.ENTER_ROOM;
    }

    @Override
    public ResultVO<EnterNoticeEntry> fillEnterNoticeMedal(EnterNoticeEntry noticeEntry) {
        return ResultVO.success(noticeEntry);
    }

    /**
     * 构建财富等级图片地址
     *
     * @param wealthInfo 财富等级信息
     * @return 图片地址
     */
    private String buildWealthUrl(WealthLevelDTO wealthInfo) {
        if (wealthInfo.getCover().startsWith("http")) {
            return wealthInfo.getCover();
        }

        String cdn = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.XIMI.getAppId()).getCdnHost();
        return UrlUtils.addCdnHost(cdn, wealthInfo.getCover());
    }

    /**
     * 是否展示欢迎按钮， 西米刚刚上线的。暂时屏蔽
     *
     * @param liveId           直播节目ID
     * @param enterNoticeEntry 进房公告消息
     * @return true: 展示，false: 不展示
     */
    @Override
    public boolean isShowButton(long liveId, long userId, EnterNoticeEntry enterNoticeEntry) {
//        if (enterNoticeEntry.getIsShowButton() == null || !enterNoticeEntry.getIsShowButton()) {
//            return false;
//        }
//        return true;
        return false;
    }

    @Override
    public List<EnterNoticeEntry> filterHideEnterNotice(List<EnterNoticeEntry> enterNoticeEntries) {
        //西米的进房公告隐身在下游做了
        return enterNoticeEntries;
    }

    @Override
    public void fillSongInfoVO(LiveCommentVO liveCommentVO, SongInfo songInfo, int commentType) {
        if (songInfo == null || commentType != WaveCommentType.ORDER_SONG) {
            return;
        }

        String cdnHost = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.XIMI.getAppId()).getCdnHost();
        SongInfoVO songInfoVO = new SongInfoVO()
                .setSongName(songInfo.getSongName())
                .setSingerName(songInfo.getSingerName())
                .setSongCover(UrlUtils.addCdnHost(cdnHost, songInfo.getSongCover()))
                .setSongId(songInfo.getSongId())
                .setTitle(songInfo.getTitle())
                .setAvatar(UrlUtils.addCdnHost(cdnHost, songInfo.getAvatar()))
                .setTargetUserId(songInfo.getTargetUserId())
                .setSeatIndex(songInfo.getSeatIndex());
        liveCommentVO.setSongInfo(songInfoVO);
        liveCommentVO.setContent(songInfo.getTitle() == null ? commentConfig.getXm().getOrderSongCommentTitle() : songInfo.getTitle());
    }

    @Override
    public void fillRoomPlaylistVO(LiveCommentVO liveCommentVO, RoomPlaylistCommentCardBean bean, int commentType) {
        if (bean == null || commentType != WaveCommentType.LIVE_ROOM_PLAYLIST) {
            return;
        }
        liveCommentVO.setExtra(CommentMapper.I.toRoomPlaylistVO(bean));
        liveCommentVO.setContent(bean.getTitle() == null ? commentConfig.getXm().getRoomPlaylistCommentTitle() : bean.getTitle());

    }

    @Override
    public boolean isBigStarNjHideMount(long njId) {
        if (StringUtils.isEmpty(commentConfig.getXm().getMountBlackListIds())) {
            return false;
        }
        return commentConfig.getXm().getMountBlackListIds().contains(String.valueOf(njId));
    }

    @Override
    public void fillRoomRouletteWheelVO(LiveCommentVO liveCommentVO, RoomRouletteWheelCommentCardBean bean, int commentType) {
        if (bean == null || commentType != WaveCommentType.LIVE_ROOM_ROULETTE_WHEEL) {
            return;
        }
        liveCommentVO.setExtra(CommentMapper.I.toRoomRouletteWheelCommentVO(bean));
        liveCommentVO.setContent(bean.getTitle() == null ? commentConfig.getXm().getRoomRouletteWheelTitle() : bean.getTitle());
    }

    @Override
    public void fillFreshUserInterestInfoVO(LiveCommentVO liveCommentVO, FreshUserInterestBean bean, int commentType) {
        if (bean == null || commentType != WaveCommentType.FRESH_USER_INTEREST) {
            return;
        }
        liveCommentVO.setExtra(CommentMapper.I.toFreshUserInterestVO(bean));
        liveCommentVO.setContent(bean.getTitle() == null ? commentConfig.getXm().getFreshUserInterestCommentTitle() : bean.getTitle());
    }

    @Override
    public EnterNoticeExtra getEnterNoticeExtra(long userId, EnterNoticeEntry enterNoticeEntry) {
        return null;
    }

    @Override
    public void fillFeedContentInfoVO(LiveCommentVO liveCommentVO, FeedContentInfoBean bean, int commentType) {
        if (bean == null || commentType != WaveCommentType.FEED_CONTENT) {
            return;
        }
        liveCommentVO.setExtra(CommentMapper.I.toFeedContentInfoVO(bean));

    }

}
