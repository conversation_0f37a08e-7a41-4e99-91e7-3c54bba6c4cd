package fm.lizhi.ocean.wave.comment.core.extension.medal;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoContext;
import fm.lizhi.ocean.wave.comment.core.manager.PlayQuestManager;
import fm.lizhi.ocean.wave.comment.core.model.dto.UserLevelMarkDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.LevelMarkVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.AnthorLevel;
import fm.lizhi.ocean.wave.comment.core.remote.bean.LevelMark;
import fm.lizhi.ocean.wave.comment.core.remote.param.UserLevelMarkParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.UserLevelMarkResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 主播等级勋章节点
 * 目前只有hy的主播等级勋章，后续其他业务线有需求再扩展
 */
@Component
public class HyAnchorLevelMedalNode implements ICommentMedalNode {

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private PlayQuestManager playerQuestManager;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoContext context) {
        int medalShowArea = context.getMedalShowArea();
        AnthorLevel anthorLevel = null;
        if (medalShowArea == GenMedalInfoContext.MedalShowArea.COMMENT_AREA.getArea()) {
            //评论的主播等级勋章是设置评论时就有的
            anthorLevel = context.getComment().getAnthorLevel();
        } else if (medalShowArea == GenMedalInfoContext.MedalShowArea.ENTER_NOTICE_AREA.getArea()) {
            //进房公告的主播等级勋章，需要在构建时查询
            anthorLevel = getUserLevel(context.getEnterNoticeEntry().getUserId());
        }

        if (anthorLevel == null || StringUtils.isBlank(anthorLevel.getCover())) {
            return Optional.empty();
        }

        BadgeImageVO badgeImageVO = new BadgeImageVO();
        badgeImageVO.setBadgeUrl(anthorLevel.getCover());
        badgeImageVO.setBadgeAspect(anthorLevel.getAspect());
        return Optional.of(Lists.newArrayList(badgeImageVO));
    }

    /**
     * 获取用户是否是陪玩
     *
     * @param uid 用户ID
     * @return 等级勋章
     */
    public AnthorLevel getUserLevel(long uid) {
        Result<UserLevelMarkDTO> result = playerQuestManager.getUserLevelMarkFromCache(uid);
        if (null != result && result.rCode() == 0) {
            List<LevelMarkVO> levelMarks = result.target().getLevelMarks();
            if (CollectionUtils.isEmpty(levelMarks)) {
                return null;
            }
            Optional<LevelMarkVO> first = levelMarks.stream().filter(s -> s.getType() == 2).findFirst();
            if (first.isPresent()) {
                LevelMarkVO levelMark = first.get();
                AnthorLevel anthorLevel = new AnthorLevel();
                anthorLevel.setLevel(levelMark.getLevel());
                anthorLevel.setCover(levelMark.getBadge());
                if (commentConfig.getHy().isDefaultLevelMedalAspectFlag()) {
                    anthorLevel.setAspect(commentConfig.getHy().getDefaultLevelMedalAspect());
                } else {
                    anthorLevel.setAspect(levelMark.getAspect().floatValue());
                }
                return anthorLevel;
            }

        }
        return null;
    }

}
