package web.ocean.wave.user.protocol;
option java_package = "fm.lizhi.ocean.wave.live.export.protocol";
option java_outer_classname = "KickOutServiceProto";

// 踢出前置流程参数
message KickOutPreProcessorParam {
    required int64 liveId = 1; // 直播ID
    required int32 tickFlag = 2; // 操作标记 1: 踢出，0:取消踢出(hy独有)
    optional int64 operateUserId = 3; // 操作用户ID
    optional int64 liveRoomId = 4; // 房间ID
    optional int64 targetUserId = 5; // 被踢用户ID
    optional int64 njId = 6; // 房主ID
}

// 踢出后置流程参数
message KickOutPostProcessorParam {
    required int64 liveId = 1; // 直播ID
    required int32 tickFlag = 2; // 操作标记 1: 踢出，0:取消踢出(hy独有)
    optional int64 operateUserId = 3; // 操作用户ID
    optional int64 liveRoomId = 4; // 房间ID
    optional int64 targetUserId = 5; // 被踢用户ID
    optional int64 njId = 6; // 房主ID
}

// KickOutProcessorService.java
// 踢出前置流程
// domain = 20019, OP = 51
message RequestKickOutPreProcessor {
    optional KickOutPreProcessorParam param = 1;    // 查询参数
}

// rCode==0 (SUCCESS) == 成功
message ResponseKickOutPreProcessor {
}


// KickOutProcessorService.java
// 踢出后置流程
// domain = 20019, OP = 52
message RequestKickOutPostProcessor {
    optional KickOutPostProcessorParam param = 1;    // 查询参数
}

// rCode==0 (SUCCESS) == 成功
message ResponseKickOutPostProcessor {
}