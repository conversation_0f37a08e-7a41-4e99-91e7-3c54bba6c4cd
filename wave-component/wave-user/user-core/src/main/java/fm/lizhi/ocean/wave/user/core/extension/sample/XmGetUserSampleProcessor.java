package fm.lizhi.ocean.wave.user.core.extension.sample;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.user.core.config.UserProviderConfig;
import fm.lizhi.ocean.wave.user.core.remote.bean.SimpleUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/1/16 16:54
 */
@Component
public class XmGetUserSampleProcessor implements GetUserSampleProcessor{

    @Autowired
    private UserProviderConfig userConfig;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public void fillOfficialInfo(SimpleUser simpleUser) {
        String officialAccount = userConfig.getXm().getOfficialAccount();
        if (StringUtils.isNotBlank(officialAccount)) {
            simpleUser.setOfficial(StringUtils.contains(officialAccount, String.valueOf(simpleUser.getUserId())));
        }
    }

}
