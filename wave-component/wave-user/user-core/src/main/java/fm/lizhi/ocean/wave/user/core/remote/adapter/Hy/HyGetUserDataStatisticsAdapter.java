package fm.lizhi.ocean.wave.user.core.remote.adapter.Hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import hy.fm.lizhi.live.data.protocol.UserDataProto;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.convert.IRemoteMethodParamAndResultAdapter;
import fm.lizhi.ocean.wave.user.core.remote.bean.GetUserDataStatisticsRequest;
import fm.lizhi.ocean.wave.user.core.remote.result.GetUserDataStatisticsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 获取用户数据统计参数适配器
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyGetUserDataStatisticsAdapter implements IRemoteMethodParamAndResultAdapter<
        GetUserDataStatisticsRequest, UserDataProto.UserDataParams,
        Result<UserDataProto.ResponseGetUserDataStatistics>, Result<GetUserDataStatisticsResponse>> {

    @Override
    public UserDataProto.UserDataParams convertParam(GetUserDataStatisticsRequest args) {
        UserDataProto.UserDataParams.Builder builder = UserDataProto.UserDataParams.newBuilder();
        builder.setUserId(args.getUserId()).setType(args.getType());
        return builder.build();
    }

    @Override
    public Result<GetUserDataStatisticsResponse> convertResult(Result<UserDataProto.ResponseGetUserDataStatistics> result) {
        int rCode = result.rCode();

        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return new Result<>(rCode, null);
        }

        UserDataProto.ResponseGetUserDataStatistics response = result.target();
        GetUserDataStatisticsResponse getUserDataStatisticsResponse = new GetUserDataStatisticsResponse();
        getUserDataStatisticsResponse.setCount(response.getCount());

        return new Result<>(rCode, getUserDataStatisticsResponse);
    }
}
