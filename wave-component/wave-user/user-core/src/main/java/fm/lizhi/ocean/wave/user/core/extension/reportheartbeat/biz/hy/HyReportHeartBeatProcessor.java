package fm.lizhi.ocean.wave.user.core.extension.reportheartbeat.biz.hy;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.context.Header;
import fm.lizhi.ocean.wave.server.common.context.ServiceContext;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.core.config.UserProviderConfig;
import fm.lizhi.ocean.wave.user.core.extension.reportheartbeat.bean.ReportHeartBeatPreBean;
import hy.fm.lizhi.live.pp.core.constants.AppHeartbeatSourceEnum;
import hy.fm.lizhi.live.pp.core.vo.AppHeartbeatMsg;
import lombok.extern.slf4j.Slf4j;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.user.core.extension.reportheartbeat.ReportHeartBeatProcessor;
import fm.lizhi.ocean.wave.user.core.extension.reportheartbeat.bean.ReportHeartBeatPostBean;
import fm.lizhi.ocean.wave.user.core.kafka.producer.UserHyKafkaProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023/5/18
 */
@Slf4j
@Component
public class HyReportHeartBeatProcessor implements ReportHeartBeatProcessor {

    @Autowired
    private UserHyKafkaProducer userHyKafkaProducer;
    @Autowired
    private UserProviderConfig userProviderConfig;

    @Override
    public ResultVO<Void> preprocessor(ReportHeartBeatPreBean data) {
        log.info("HY_ReportHeartBeat preprocessor...");

        return ResultVO.success();
    }

    @Override
    public ResultVO<Void> postprocessor(ReportHeartBeatPostBean data) {
        log.info("HY_ReportHeartBeat processor... ");
        ServiceContext context = ContextUtils.getContext();
        Header header = context.getHeader();

        AppHeartbeatMsg msg = new AppHeartbeatMsg();
        msg.setAppId(header.getAppId());
        msg.setSubAppId(0);
        msg.setStage(1);
        msg.setUserId(context.getUserId());
        msg.setAppLang("zh");
        msg.setDeviceId(header.getDeviceId());
        msg.setDeviceType(header.getDeviceType());
        msg.setVersionName("");
        msg.setReportTime(System.currentTimeMillis());
        msg.setChannelId("");
        msg.setLaunch(data.isFirst());
        msg.setSource(AppHeartbeatSourceEnum.PC.getValue());

        userHyKafkaProducer.send(userProviderConfig.getHy().getHyHeartBeatTopic(), null, JsonUtil.dumps(msg));
        return ResultVO.success();
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.HY;
    }
}
