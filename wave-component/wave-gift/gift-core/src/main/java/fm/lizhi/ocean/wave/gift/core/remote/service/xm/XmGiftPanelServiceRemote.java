package fm.lizhi.ocean.wave.gift.core.remote.service.xm;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.constants.LiveModeEnum;
import fm.lizhi.ocean.wave.api.live.param.GetLiveModeRequestParam;
import fm.lizhi.ocean.wave.api.live.result.GetLiveModeResult;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.common.util.EnvUtils;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.gift.core.config.GiftConfig;
import fm.lizhi.ocean.wave.gift.core.constants.GiftGroupGiftTypeEnum;
import fm.lizhi.ocean.wave.gift.core.constants.GiftSourceMapping;
import fm.lizhi.ocean.wave.gift.core.model.request.GiftBoxParam;
import fm.lizhi.ocean.wave.gift.core.model.request.GiftGroupParam;
import fm.lizhi.ocean.wave.gift.core.model.request.GiftListParam;
import fm.lizhi.ocean.wave.gift.core.model.result.GiftGroupResult;
import fm.lizhi.ocean.wave.gift.core.model.result.GiftListResult;
import fm.lizhi.ocean.wave.gift.core.model.vo.GiftGroupVO;
import fm.lizhi.ocean.wave.gift.core.model.vo.GiftVO;
import fm.lizhi.ocean.wave.gift.core.remote.adapter.xm.XmGiftPanelAdapter;
import fm.lizhi.ocean.wave.gift.core.remote.service.IGiftPanelServiceRemote;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.user.api.UserGroupService;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.result.GetSimpleUserResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.gift.api.GiftGroupNewService;
import xm.fm.lizhi.live.gift.api.GiftGroupService;
import xm.fm.lizhi.live.gift.enums.GiftGroupSceneEnum;
import xm.fm.lizhi.live.gift.enums.SourceEnum;
import xm.fm.lizhi.live.gift.protocol.GiftGroupNewProto;
import xm.fm.lizhi.live.gift.protocol.GiftGroupProto;
import xm.fm.lizhi.live.trade.dto.avatar.Goods;
import xm.fm.lizhi.live.trade.dto.avatar.resp.GetGoodsList;
import xm.fm.lizhi.live.trade.services.AvatarStoreSpringService;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmGiftPanelServiceRemote extends RemoteServiceInvokeFacade implements IGiftPanelServiceRemote {

    @Autowired
    private GiftGroupNewService giftGroupNewService;

    @Autowired
    private XmGiftPanelAdapter xmGiftPanelAdapter;

    @Autowired
    private GiftGroupService giftGroupService;

    @Autowired
    private UserGroupService userGroupService;

    @Autowired
    private LiveService liveService;


    @Autowired
    private GiftConfig giftConfig;

    @Autowired
    private AvatarStoreSpringService avatarStoreSpringService;

    @Autowired
    private UserService userService;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.XIMI.equals(evnEnum);
    }


    /**
     * 获取礼物分组
     */
    @Override
    public Result<GiftGroupResult> getGiftGroup(GiftGroupParam param) {

        GiftGroupNewProto.QueryGiftGroupRequest request = xmGiftPanelAdapter.convertGiftGroupRequest(param, getScene(param.getSource(), param.getLiveId()));
        Result<GiftGroupNewProto.ResponseQueryGiftGroup> result = giftGroupNewService.queryGiftGroup(request);

        if (RpcResult.isFail(result)){
            log.warn("XmGiftGroupServiceRemote.getGiftGroup fail, rCode={}, userId={}", result.rCode(), param.getUserId());
            return new Result<>(IGiftPanelServiceRemote.GET_GIFT_GROUP_ERROR, null);
        }

        GiftGroupResult giftGroupResult = xmGiftPanelAdapter.convertGiftGroupResult(result);
        addConfigGroup(giftGroupResult.getGroups());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, giftGroupResult);
    }

    /**
     * 添加配置的礼物分组
     * @param vos
     */
    private void addConfigGroup(List<GiftGroupVO> vos) {

        GiftConfig.XM config = giftConfig.getXm();

        vos.add(new GiftGroupVO()
                .setTitle(config.getAvatarGiftGroupName())
                .setGroupId(config.getAvatarGroupId())
                .setDisplayLocationUserGroup(config.getAvatarGiftGroupUserGroupIds())
        );

    }


    @Override
    public Result<GiftListResult> listByGroup(GiftListParam param) {

        if (param.getGroupId().equals(String.valueOf(giftConfig.getXm().getAvatarGroupId()))){
            return getAvatarGiftList(param);
        }

        Result<GiftGroupProto.ResponseGiftByGroupId> result = giftGroupService.queryGiftByGroupId(Long.parseLong(param.getGroupId()), EnvUtils.isOffice());

        if (RpcResult.isFail(result)){
            log.warn("XmGiftGroupServiceRemote.listByGroup fail, rCode={}, userId={}", result.rCode(), param.getUserId());
            return new Result<>(IGiftPanelServiceRemote.GET_GIFT_GROUP_ERROR, null);
        }

        return xmGiftPanelAdapter.convertGiftResult(result);
    }



    @Override
    public Result<GiftListResult> getGiftBoxRelation(GiftBoxParam param) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GiftListResult.builder().build());
    }

    @Override
    public List<GiftVO> getGiftByGroup(long groupId, String userId) {

        Result<GiftGroupProto.ResponseGiftByGroupId> result = giftGroupService.queryGiftByGroupId(groupId, EnvUtils.isOffice());
        if (RpcResult.isFail(result)){
            log.info("xmGiftGroupServiceRemote.getGiftByGroup fail, rCode={}, groupId={}, userId={}", result.rCode(), groupId, userId);
            return Collections.emptyList();
        }

        return xmGiftPanelAdapter.convertGiftResult(result).target().getGifts();
    }

    /**
     * 获取形象装扮礼物
     */
    private Result<GiftListResult> getAvatarGiftList(GiftListParam param) {
        Result<GetSimpleUserResult> userResult = userService.getSimpleUserByCache(Long.parseLong(param.getUserId()));
        int gender = 0;
        if (RpcResult.isFail(userResult)){
            gender = Optional.ofNullable(userResult.target()).map(GetSimpleUserResult::getSimpleUser).map(SimpleUser::getGender).orElse(0);
        }

        Result<GetGoodsList> result = avatarStoreSpringService.getGoods(xmGiftPanelAdapter.convertAvatarGiftRequest(param, gender));
        if (RpcResult.isFail(result)){
            log.warn("xm getAvatarGiftList is fail.rCode={}, liveId={}, userId={}", result.rCode(), param.getLiveId(), param.getUserId());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GiftListResult.builder().build());
        }

        List<Goods> goodsList = result.target().getList();
        List<GiftVO> vos = xmGiftPanelAdapter.convertAvatarGiftList(goodsList);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GiftListResult.builder().gifts(vos).total(vos.size()).build());
    }



    private int getScene(int source, String liveId) {
        int bizSource = GiftSourceMapping.getBizGiftSource(source, BusinessEvnEnum.XIMI);


        AtomicInteger scene = new AtomicInteger(pp.fm.lizhi.live.gift.enums.GiftGroupSceneEnum.NORMAL.getValue());
        if (bizSource == SourceEnum.Amusement.getValue()) {
            scene.set(pp.fm.lizhi.live.gift.enums.GiftGroupSceneEnum.AMUSEMENT.getValue());
        } else if (bizSource == SourceEnum.IM.getValue()) {
            scene.set(pp.fm.lizhi.live.gift.enums.GiftGroupSceneEnum.IM.getValue());
        } else if (bizSource == SourceEnum.Reward.getValue()) {
            scene.set(pp.fm.lizhi.live.gift.enums.GiftGroupSceneEnum.REWARD.getValue());
        } else if (bizSource == SourceEnum.TREASURE_BOX_GUEST.getValue() || bizSource == SourceEnum.TREASURE_BOX_LISTENER.getValue()) {
            scene.set(pp.fm.lizhi.live.gift.enums.GiftGroupSceneEnum.TREASURE_BOX.getValue());
        }

        // 如果当前模式是电台或者互动，切换到对应的礼物
        if (scene.get() != pp.fm.lizhi.live.gift.enums.GiftGroupSceneEnum.IM.getValue()) {
            Result<GetLiveModeResult> liveMode = liveService.getLiveMode(GetLiveModeRequestParam.builder().liveId(Long.parseLong(liveId)).build());
            if (RpcResult.isSuccess(liveMode)){
                // 非IM场景，判断是否电台或者互动
                Optional.ofNullable(liveMode.target()).map(GetLiveModeResult::getLiveModeEnum).ifPresent(model -> {
                    if (model == LiveModeEnum.NORMAL_RADIO){
                        scene.set(pp.fm.lizhi.live.gift.enums.GiftGroupSceneEnum.RADIO_MODE.getValue());
                    } else if (model == LiveModeEnum.NORMAL_INTERACTIVE) {
                        scene.set(GiftGroupSceneEnum.INTERACTIVE_MODE.getValue());
                    }

                });
            }
        }

        return scene.get();
    }


    private boolean checkInUserGroups(String userGroups, long currUserId) {
        if (StrUtil.isEmpty(userGroups)){
            return true;
        }

        // 用户组id列表
        List<Long> groupIds = Stream.of(userGroups.split(","))
                .map(Long::parseLong).collect(Collectors.toList());

        boolean isPass = false;
        for (Long gid : groupIds) {
            Result<Boolean> userInGroup = userGroupService.isUserInGroup(gid, currUserId, 0);
            if (RpcResult.isSuccess(userInGroup) && userInGroup.target()) {
                isPass = true;
                break;
            }
        }

        return isPass;
    }

}
