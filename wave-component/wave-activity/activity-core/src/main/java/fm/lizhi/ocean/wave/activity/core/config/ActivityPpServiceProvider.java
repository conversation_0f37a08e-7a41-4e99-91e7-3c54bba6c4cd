package fm.lizhi.ocean.wave.activity.core.config;

import fm.lizhi.pp.content.artrcmd.api.OfficialActivityService;
import fm.lizhi.ocean.wave.common.auto.route.common.annotation.ScanBusinessProviderAPI;
import fm.lizhi.payment.api.PaymentAccountQueryService;
import fm.lizhi.pp.content.assistant.api.BannerManagementService;
import fm.lizhi.pp.content.assistant.api.PendantService;
import fm.lizhi.pp.social.api.AccompanyOrderService;
import fm.lizhi.pp.vip.api.PpVipService;
import org.springframework.context.annotation.Configuration;
import pp.fm.lizhi.live.data.api.LiveStateService;
import pp.fm.lizhi.pp.mask.api.MaskService;
import pp.fm.lizhi.pp.match.api.PpMatchCourseService;
import pp.fm.lizhi.pp.pk.api.PkMatchManageService;
import pp.fm.lizhi.pp.rank.api.RankingService;

@Configuration
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = LiveStateService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = RankingService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PaymentAccountQueryService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PendantService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpVipService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PkMatchManageService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpMatchCourseService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = AccompanyOrderService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = BannerManagementService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = MaskService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = OfficialActivityService.class)
}
)
public class ActivityPpServiceProvider {
}
