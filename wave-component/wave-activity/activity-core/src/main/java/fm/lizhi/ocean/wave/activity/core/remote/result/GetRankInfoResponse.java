package fm.lizhi.ocean.wave.activity.core.remote.result;

import fm.lizhi.ocean.wave.activity.core.remote.bean.RankInfo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class GetRankInfoResponse {
    /**
     * 榜单信息列表
     */
    List<RankInfo> rankInfos;
    /**
     * 是否是查看榜单用户具体分数的白名单用户，true表示是（西米在用）
     */
    boolean isShowScoreWhiteList;

}
