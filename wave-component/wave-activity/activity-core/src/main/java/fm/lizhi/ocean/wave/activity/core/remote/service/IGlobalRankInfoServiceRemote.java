package fm.lizhi.ocean.wave.activity.core.remote.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.activity.core.remote.param.GetGlobalRankScoreRequest;
import fm.lizhi.ocean.wave.activity.core.remote.param.GetRankInfoRequest;
import fm.lizhi.ocean.wave.activity.core.remote.result.GetRankInfoResponse;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.IBaseRemoteServiceInvoker;

/**
 * <AUTHOR>
 */
public interface IGlobalRankInfoServiceRemote extends IBaseRemoteServiceInvoker {
    /**
     * 获取平台榜单信息
     *
     * @param param
     * @return
     */
    Result<GetRankInfoResponse> getGlobalRankInfo(GetRankInfoRequest param);

    /**
     * 获取平台榜单成员分数
     *
     * @param param
     * @return
     */
    Result<Long> getGlobalRankMemberScore(GetGlobalRankScoreRequest param);

    /**
     * 获取平台榜单参数错误
     */
    int GET_GLOBAL_RANK_INFO_ILLEGAL_PARAM = 1;
    /**
     * 获取平台榜单失败
     */
    int GET_GLOBAL_RANK_INFO_FAIL = 2;
    /**
     * 获取平台榜单成员分数失败
     */
    int GET_GLOBAL_RANK_MEMBER_SCORE_FAIL = 1;
}
