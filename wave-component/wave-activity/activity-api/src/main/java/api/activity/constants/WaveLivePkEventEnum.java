package api.activity.constants;

import lombok.Getter;

/**
 * PK事件定义
 * <AUTHOR>
 */

@Getter
public enum WaveLivePkEventEnum {

    /**
     * pk邀请、匹配
     */
    PK_INVITE_EVENT(1),

    /**
     * 取消邀请、匹配
     */
    PK_CANCEL_INVITE_EVENT(2),

    /**
     * 接受邀请、匹配成功
     */
    PK_ACCEPT_INVITE_EVENT(3),

    /**
     * 拒绝邀请
     */
    PK_REJECT_INVITE_EVENT(4),

    /**
     * PK状态变化（认输、结束PK、PK时间结束、惩罚时间结束、首杀超时、惊喜时刻超时）
     * 先聚合，后续需要对每个状态做更精细化的逻辑处理时，再拆分即可
     */
    PK_STATUS_CHANGE_EVENT(20),

    /**
     * PK尾声时刻
     */
    PK_FINAL_TIME_EVENT(6),

    /**
     * 匹配/邀请超时
     */
    PK_INVITE_TIMEOUT_EVENT(7),


    /**
     * 加入连线
     */
    PK_JOIN_LINE_EVENT(10),

    /**
     * 退出连线
     */
    PK_EXIT_LINE_EVENT(11),

    /**
     * 收到邀请/匹配
     */
    PK_INVITE_RECEIVE_EVENT(12),

    /**
     * 加成时刻
     */
    PK_CELEBRATION_EVENT(13),

    /**
     * 收礼事件
     */
    PK_GIFT_RECEIVE_EVENT(14),

    /**
     * PK中关播
     */
    PK_CLOSE_LIVE_EVENT(15),

    /**
     * 匹配中关播
     */
    PK_MATCH_CLOSE_LIVE_EVENT(16),
    /**
     * 邀请失败弹窗
     */
    PK_INVITE_FAIL_EVENT_POP(17),

    /**
     * 惩罚中关播
     */
    PK_PUNISH_CLOSE_LIVE_EVENT(18),
    /**
     * 抢得首杀
     */
    PK_FIRST_BLOOD_EVENT(19),

    ;


    private final int event;


    WaveLivePkEventEnum(int event) {
        this.event = event;
    }
}
