
package api.activity.bean.pk;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PkData {

    private String matchId;


    /**
     * 我的PK直播数据
     */
    private PkLiveData myPkLiveData;

    /**
     * 是否开启连线
     */
    private Boolean micIsOpen;


    /**
     * 对方PK直播数据
     */
    private PkLiveData targetPkLiveData;

    /**
     * pk中or惩罚中的剩余秒数
     */
    private Integer remainingTime;

    /**
     * pk状态
     * @see api.activity.constants.WaveLivePkStatusEnum
     */
    private Integer status;



    /* ================== XM 独有↓ ===================*/

    /**
     * 是否邀请方（XM、PP、HY独有）
     */
    private Boolean inviter;

    private FirstBloodInfo firstBloodInfo;


    /* ================== PP 独有↓ ===================*/
    /**
     * PK 阶段（PP独有）
     * 0、正常阶段； 1、任务时刻 ；2、加成时刻
     */
    private Integer stage;

    /**
     * pk任务时刻信息
     */
    private PkTaskTimeInfo pkTaskTimeInfos;

    /**
     * 排行榜URL
     */
    private String rankUrl;

    /**
     * PK模式 1-单人 2-多人
     */
    private Integer pkMode;

    /**
     * 开始匹配时间戳（单位毫秒）
     */
    private Long startTime;

    /**
     * 结束匹配时间戳（单位毫秒）
     */
    private Long endTime;


}