package api.activity.result;

import api.activity.bean.pk.PkData;
import api.activity.bean.pk.PkInvitation;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;

import static api.activity.constants.WaveLivePkStatusEnum.PK_NO_OPEN_STATUS;

/**
 * 发起匹配、邀请的响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PkInfoResult {
//    private Long liveId;
//    private Long userId;
//    private Long targetLiveId;
//    private Integer timeId;

    /**
     * PK 数据
     */
    private PkData pkData;


    /* ================== PP 独有↓ ===================*/

    /**
     * PK提示语
     */
    private List<String> pkTips = Collections.emptyList();

    /* ================== XM 独有↓ ===================*/

    /**
     * PK邀请数据
     */
    private PkInvitation pkInvitationData;

    public Boolean isNull(){
        return pkData == null || pkData.getStatus().equals(PK_NO_OPEN_STATUS.getStatus());
    }
}
