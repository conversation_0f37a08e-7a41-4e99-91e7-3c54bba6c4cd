package api.activity.constants;

import lombok.Getter;

/**
 *
 * PK 操作状态
 * <AUTHOR>
 */
@Getter
public enum WaveLivePkOperateEnum {


    /**
     * pk邀请
     */
    PK_INVITE(1),

    /**
     * 取消邀请
     */
    PK_CANCEL_INVITE(2),

    /**
     * 接受邀请
     */
    PK_ACCEPT_INVITE(3),

    /**
     * 拒绝邀请
     */
    PK_REJECT_INVITE(4),

    /**
     * 认输
     */
    PK_GIVE_UP(5),

    /**
     * 结束PK
     */
    PK_DO_FINISH(6),

    /**
     * 加入连线
     */
    PK_JOIN_LINE(10),

    /**
     * 退出连线
     */
    PK_EXIT_LINE(11),


    /**
     * 庆祝撒花
     */
    PK_CELEBRATION(12),

    ;

    WaveLivePkOperateEnum(int operation) {
        this.operation = operation;
    }

    private final int operation;


    public static WaveLivePkOperateEnum getEnum(int operation) {
        for (WaveLivePkOperateEnum value : values()) {
            if(value.operation == operation) {
                return value;
            }
        }
        return null;
    }
}
