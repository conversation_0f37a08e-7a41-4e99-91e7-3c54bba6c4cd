
package api.activity.bean.pk;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PkLiveData {

    /**
     *PP独有: 是否是处于加成时刻
     */
    private Boolean isBonusTime;

    /**
     * PP独有:加成时刻倒计时
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long markTimeCountdown;


    /**
     * PP独有:等级/段位信息
     */
    private PkRankInfo rankInfo;

    /**
     * 贡献榜用户列表
     */
    private List<PkTopUser> topUserList = Collections.emptyList();

    /**
     * 结果状态 0-无状态 1-赢 2-输 3-平局
     */
    private Integer winStatus;

    /**
     * 分数
     */
    private Long pkScore;

    /**
     * 直播间信息
     */
    private PkLive live;


    /**
     * HY独有:mvp用户
     */
    private PkMvpUser mvpUser;
}