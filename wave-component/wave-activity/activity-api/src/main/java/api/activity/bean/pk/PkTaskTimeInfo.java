
package api.activity.bean.pk;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PkTaskTimeInfo {

    /**
     * 直播id
     */
    private String liveId;

    /**
     * 任务标题
     */
    private String taskTitle;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 任务目标
     */
    private Integer taskTarget;

    /**
     * 任务进度
     */
    private Integer taskProgress;

    /**
     * 加成时刻文字描述
     */
    private String markTimeDesc;

    /**
     * 加成时刻加成比例
     */
    private Double bonusRate;
}