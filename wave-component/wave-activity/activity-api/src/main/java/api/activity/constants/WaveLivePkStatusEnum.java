package api.activity.constants;

import lombok.Getter;

import java.util.Objects;

/**
 *
 * PK 状态
 * <AUTHOR>
 */
@Getter
public enum WaveLivePkStatusEnum {


    /**
     * 未开启，也耦合已结束状态
     */
    PK_NO_OPEN_STATUS(0),

    /**
     * 匹配/邀请中
     */
    PK_INVITING_STATUS(1),

    /**
     * PK中
     */
    PK_ING_STATUS(2),

    /**
     * 惩罚中（结果公布） HY,XM独有
     */
    PK_PUNISHMENT_ING_STATUS(3),

    /**
     * PK尾声时间（PP结果公布）
     */
    PK_FINAL_TIME_STATUS(4),

    /**
     * 匹配失败 HY,xm独有，也属于最终状态
     */
    PK_INVITE_FAIL_STATUS(5),
    ;

    WaveLivePkStatusEnum(Integer status) {
        this.status = status;
    }

    private final Integer status;


    public boolean isEnd() {
        return this == PK_NO_OPEN_STATUS || this == PK_INVITE_FAIL_STATUS;
    }

    public static WaveLivePkStatusEnum getEnum(Integer status) {
        for (WaveLivePkStatusEnum value : values()) {
            if(Objects.equals(value.status, status)) {
                return value;
            }
        }
        return null;
    }
}
