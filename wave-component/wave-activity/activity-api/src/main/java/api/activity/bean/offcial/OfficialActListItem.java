package api.activity.bean.offcial;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OfficialActListItem {

    private Long id;
    /**
     * 活动名称
     */
    private String name;
    /**
     * 开始时间戳
     */
    private Long startTime;
    /**
     * 结束时间戳
     */
    private Long endTime;
    /**
     * 状态
     * {@link OfficialActConstants.ActDaoMapDtoStatus}
     */
    private int status;
}
