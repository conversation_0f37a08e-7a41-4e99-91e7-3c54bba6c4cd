
package api.activity.bean.pk;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PkInvitation {

    /**
     * 对方直播间信息
     */
    private PkLive targetLive;

    /**
     * 我方直播间信息
     */
    private PkLive live;

    /**
     * 提示语描述标识
     */
    private Integer bizType;

    /**
     * 邀请标题
     */
    private String title;

    /**
     * 邀请剩余时间，单位：秒
     */
    private Integer pkInvitationTime;

    /**
     * 持续时间描述
     */
    private String durationDesc;


    private String desc;


    /**
     * Xm独有 参与类型:1-定向邀请 2-随机匹配
     */
    private Integer joinType;


}