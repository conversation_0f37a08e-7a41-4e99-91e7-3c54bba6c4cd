package api.activity.api;


import api.activity.constants.WaveLivePkStatusEnum;
import api.activity.param.PkInfoParam;
import api.activity.result.PkInfoResult;
import fm.lizhi.commons.service.client.pojo.Result;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/23
 */
public interface LivePkService {


    /**
     * PK信息
     * @param userId
     * @param param
     * @return
     */
    Result<PkInfoResult> pkInfo(long userId, PkInfoParam param);

    /**
     * 校验是否有PK权限 - PP专用
     * @param userId
     * @param liveId
     * @param njId
     * @return
     */
    Result<Boolean> checkUserPkAuthority(long userId, long liveId, long njId);
}
