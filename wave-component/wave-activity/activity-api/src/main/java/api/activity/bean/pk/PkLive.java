package api.activity.bean.pk;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PkLive {

    /**
     * 直播ID
     */
    private String id;

    /**
     * 直播名字
     */

    private String name;

    /**
     * 直播封面
     */
    private String imageUrl;

    /**
     * 用户ID
     */
    private String userId;
}
