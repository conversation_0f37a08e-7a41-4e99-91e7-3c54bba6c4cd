package api.activity.bean.pk;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * pk邀请人信息
 *
 * <AUTHOR>
 * @date 2025/1/13 17:37
 */
@Data
@Accessors(chain = true)
public class PkInviter {
    /**
     * 对方（邀请人）直播信息
     */
    private PkLive targetLive;
    /**
     * 邀请剩余时间，单位：秒
     */
    private Integer pkInvitationTime;
    /**
     * 持续时间描述
     */
    private String durationDesc;
    /**
     * 邀请ID
     */
    private String inviteId;
    /**
     * 类型：1、系统匹配，2、定向匹配
     */
    private Integer type;
}
