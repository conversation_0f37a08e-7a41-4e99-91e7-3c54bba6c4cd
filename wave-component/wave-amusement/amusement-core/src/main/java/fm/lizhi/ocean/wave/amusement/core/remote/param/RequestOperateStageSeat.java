package fm.lizhi.ocean.wave.amusement.core.remote.param;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/11/16 19:08
 */
@Data
@Accessors(chain = true)
public class RequestOperateStageSeat {
    /**
     * 直播id
     */
    private long liveId;

    /**
     * 当前操作id  西米 = （上舞台的用户id）
     */
    private long userId;

    /**
     * 0：下台聊天,1: 上台主持 ，2：上台唱歌
     */
    private int operation;

    /**
     * 被操作的用户id
     */
    private Long targetUserId;

    /**
     * 记录id
     */
    private Long singRecordId;

}
