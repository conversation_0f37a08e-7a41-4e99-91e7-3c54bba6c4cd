package fm.lizhi.ocean.wave.amusement.core.model.vo;

import fm.lizhi.ocean.wave.amusement.core.constant.TeamWarSeatEnum;
import fm.lizhi.ocean.wave.amusement.core.constant.TeamWarStateEnum;
import fm.lizhi.ocean.wave.api.live.constants.LiveModeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/12/1 10:12
 */
@Data
@Accessors(chain = true)
public class TeamWarVo {

    /**
     * 魔法团战状态
     * @see fm.lizhi.ocean.wave.amusement.core.constant.TeamWarStateEnum
     */
    private Integer state;

    /**
     * 开始时间，时间戳
     */
    private String startTime;

    /**
     * 剩余时间，单位秒
     */
    private Integer remainingTime = 0;

    /**
     * 团战主题
     */
    private String teamWarTheme;

    /**
     * 战队A信息
     */
    private TeamInfoVo ateamInfo;

    /**
     * 战队B信息
     */
    private TeamInfoVo bteamInfo;


    /**
     * 构建默认对象，避免前端异常崩溃
     */
    public static TeamWarVo buildDefault(LiveModeEnum modeEnum){

        return new TeamWarVo()
                .setState(TeamWarStateEnum.NORMAL.getValue())
                .setStartTime("0")
                .setRemainingTime(0)
                .setTeamWarTheme("")
                .setAteamInfo(new TeamInfoVo().setTeamLevel(0).setNextFullCharm(0).setCurrentBaseCharm(0).setSeatNums(TeamWarSeatEnum.getATeamSeat(modeEnum)))
                .setBteamInfo(new TeamInfoVo().setTeamLevel(0).setNextFullCharm(0).setCurrentBaseCharm(0).setSeatNums(TeamWarSeatEnum.getBTeamSeat(modeEnum)))
        ;
    }
}
