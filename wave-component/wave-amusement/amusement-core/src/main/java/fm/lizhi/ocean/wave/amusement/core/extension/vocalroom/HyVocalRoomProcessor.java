package fm.lizhi.ocean.wave.amusement.core.extension.vocalroom;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.amusement.core.config.AmusementConfig;
import fm.lizhi.ocean.wave.amusement.core.constant.AmusementMsgCodes;
import fm.lizhi.ocean.wave.amusement.core.extension.vocalroom.bean.ChangeSeatStatusPreCheckContext;
import fm.lizhi.ocean.wave.amusement.core.model.param.SaveWaitingSingParam;
import fm.lizhi.ocean.wave.amusement.core.model.param.SingSaveSongParam;
import fm.lizhi.ocean.wave.amusement.core.model.vo.SingSongSortVo;
import fm.lizhi.ocean.wave.amusement.core.remote.result.ResponseSaveSingerSong;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:42
 */
@Slf4j
@Component
public class HyVocalRoomProcessor implements IVocalRoomProcessor{

    @Autowired
    private AmusementConfig amusementConfig;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public boolean changeSeatStatusPreCheck(ChangeSeatStatusPreCheckContext context) {
        return false;
    }

    @Override
    public ResultVO<Void> saveWaitingSingPreCheck(SaveWaitingSingParam param) {
        if(StringUtils.isEmpty(param.getSongName())){
            return ResultVO.paramError("请先填写歌曲名称");
        }
        Integer maxLength = amusementConfig.getHy().getSingSongNameLength();
        if (param.getSongName().split("").length > maxLength) {
            return ResultVO.paramError("歌曲名称不能超过"+maxLength+"个字符");
        }
        return ResultVO.success();
    }

    @Override
    public ResultVO<Void> saveSingerSongPreCheck(SingSaveSongParam param) {
        List<SingSongSortVo> songs = param.getSongs();
        if (CollectionUtils.isEmpty(songs)) {
            return ResultVO.paramError("歌曲不可为空");
        }
        Integer maxLength = amusementConfig.getHy().getSingSongNameLength();
        for (SingSongSortVo song : songs) {
            if (StringUtils.isEmpty(song.getName())) {
                return ResultVO.paramError("歌曲名称不可为空");
            }
            if (song.getName().split("").length > maxLength) {
                return ResultVO.paramError("歌曲名称不能超过"+ maxLength +"个字符");
            }
        }
        return ResultVO.success();
    }

    @Override
    public ResultVO<Void> saveSingerSongAfter(Result<ResponseSaveSingerSong> result) {
        Integer code = result.target().getCode();
        // 业务服务code为空或者等于0表示成功
        if (code != null && !code.equals(0)) {
            return ResultVO.failure(AmusementMsgCodes.SING_SONG_SAVE_SONG_FAIL.getCode()
                    , result.target().getDesc());
        }
        return ResultVO.success();
    }
}
