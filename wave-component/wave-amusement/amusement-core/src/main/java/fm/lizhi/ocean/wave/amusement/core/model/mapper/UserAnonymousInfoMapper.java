package fm.lizhi.ocean.wave.amusement.core.model.mapper;

import fm.lizhi.ocean.wave.amusement.core.remote.bean.UserAnonymousInfoBean;
import fm.lizhi.xm.live.flow.live.model.dto.UserAnonymousInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 匿名房，用户的匿名信息装换
 *
 * <AUTHOR>
 */
@Mapper(
        //注入策略 spring bean注入
        componentModel = MappingConstants.ComponentModel.SPRING,
        //未映射目标属性的报告策略
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        //确定何时对bean映射的源属性值包含空检查
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface UserAnonymousInfoMapper {

    UserAnonymousInfoMapper I = Mappers.getMapper(UserAnonymousInfoMapper.class);
    List<UserAnonymousInfoBean> toUserAnonymousInfoBeans(List<UserAnonymousInfoDto> data);
}
