package fm.lizhi.ocean.wave.api.amusement.api;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.api.amusement.bean.AmusementVoiceCallInfo;
import fm.lizhi.ocean.wave.api.amusement.param.CreateVoiceCallParam;
import fm.lizhi.ocean.wave.api.amusement.param.OperateVoiceCallParam;
import fm.lizhi.ocean.wave.api.amusement.result.GetCallTargetResult;

/**
 * 语音通话服务
 * <AUTHOR>
 * @date 2024/8/7 14:55
 */
public interface AmusementVoiceCallService {

    /**
     * 获取用户当前通话中的对象
     * @param userId
     * @return
     */
    Result<GetCallTargetResult> getCallTarget(Long userId);

    /**
     * 创建语音通话
     * @param param
     * @return
     */
    Result<AmusementVoiceCallInfo> createVoiceCall(CreateVoiceCallParam param);

    /**
     * 操作通话
     * @param param
     * @return
     */
    Result<AmusementVoiceCallInfo> operateVoiceCall(OperateVoiceCallParam param);

    int CREATE_VOICE_CALL_ERROR = 1;

    int OPERATE_VOICE_CALL_ERROR = 1;

    int GET_CALL_TARGET_ERROR = 1;

    /**
     * 当前正在通话中
     */
    int COMMON_CALL_STATUS_CALLING = 100;

    /**
     * 一方直播中
     */
    int COMMON_CALL_STATUS_LIVING = 101;
}
